
/******************** (C) COPYRIGHT 2012 BSM ********************
* File Name          : app_ad.h
* Author             : 
* Version            : V1.0
* Date               : 
* Description        : 
********************************************************************************/

#ifndef __BSP_AD_H__
#define __BSP_AD_H__

/* Includes ------------------------------------------------------------------*/
#include "stm32g4xx.h"

#define ADC1_DR_ADRESS      ((uint32_t)(ADC1_BASE + ADC_DR_OFFSET))
#define ADC2_DR_ADRESS      ((uint32_t)(ADC2_BASE + ADC_DR_OFFSET))
#define ADC3_DR_ADRESS      ((uint32_t)(ADC3_BASE + ADC_DR_OFFSET))
#define ADC4_DR_ADRESS      ((uint32_t)(ADC4_BASE + ADC_DR_OFFSET))
#define ADC5_DR_ADRESS      ((uint32_t)(ADC5_BASE + ADC_DR_OFFSET))


#define ADC1_CHANNELS       (0)
#define ADC2_CHANNELS       (7)
#define ADC3_CHANNELS       (5)
#define ADC4_CHANNELS       (13)
#define ADC5_CHANNELS       (10)


#define  AC_SAMPLE_POINT    100
typedef struct 
{
    short      temp[AC_SAMPLE_POINT]; 
    float      real;
    float      real_filter;
    float      buff[AC_SAMPLE_POINT];
    short      index;
    float      avg;
    double     sum;
    
    float      vad;
}AC_DataT;

extern volatile  rt_uint16_t ADC1_ValTemp[ADC1_CHANNELS];
extern volatile  rt_uint16_t ADC2_ValTemp[ADC2_CHANNELS];
extern volatile  rt_uint16_t ADC3_ValTemp[ADC3_CHANNELS];
extern volatile  rt_uint16_t ADC4_ValTemp[ADC4_CHANNELS];
extern volatile  rt_uint16_t ADC5_ValTemp[ADC5_CHANNELS];

int  System_hw_ADC_Init(void);
void adc_sample_task(void);

#endif


/******************* (C) COPYRIGHT 2013 Group *****END OF FILE****/

