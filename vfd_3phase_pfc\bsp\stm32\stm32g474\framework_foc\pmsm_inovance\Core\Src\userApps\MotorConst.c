#include "SystemDefine.h"
#include "MotorDefine.h"


Uint  const gInvVoltageInfo380T[9] = {
380,    //第0位:380代表逆变器的电压等级380V,最后赋值的变量为：gInvInfo.InvVolt；
3500,   //第1位:3500代表逆变器的欠压点350.0V,一位小数点,最后赋值的变量为 ：gInvInfo.InvLowUdcStad；
8100,   //第2位:8100代表逆变器的过压点810.0V,一位小数点,最后赋值的变量为 ：gInvInfo.InvUpUDC；
5374,   //第3位:5374代表逆变器的母线电压基准值537.4V,一位小数点，最后赋值的变量为：gInvInfo.BaseUdc；计算方法为：380*sqrt(2)=537.4 V。
8,      //第4位:驱动器允许的机型下限,和第5位一起决定驱动器最大和最小功率等级
34,     //第5位:驱动器允许的机型上限。
16,     //第6位:目前并未使用。
9950,   /*第7位:代表母线电压的定标系数,由硬件决定原意是表示3.3V时对应的母线电压是
        多少(3.3V == 1100V)，按照提供的数值应该填入11000，对应变量：gUDC.uDCADCoff
        */
53      //第8位:暂未使用,最终用于计算母线电压的电压转换系数是 gUDC.Coff
};
Uint const gDeadBandTable[14] =
{
//	12  13  14  15  16  17  18  19  20  21  22  23  24   25
	192,192,192,192,240,240,240,288,288,288,288,288,288, 288
};
Uint const gDeadCompTable[14] =
{
//	12  13  14  15  16  17  18  19  20  21  22  23  24   25
	78, 87, 87, 87, 117,117,117,129,129,129,135,135,120, 120
};


Uint const gInvCurrentTable380T[27] =         // 380V,480V
{
	210,   380,   510,   880,  1095,	     //8~12     PVPB07 机型12
    1700,  2500,  2914,  3700, 4500,	     //13~17  PVPB08 机型15
    6000,  7500,  19860, 11000,	             //18~21 以上电流数据包含两个小数点 PVPB09 机型 20
    
    1520,  1760,  2100,  2530, 3040,         //22~26 以下电流数据包含一个小数点
    3770,  4260,  4650,  5200, 5850,         //27~31
    6500,  7250,  8200                       //32~34
};











