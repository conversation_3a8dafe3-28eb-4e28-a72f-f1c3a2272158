#include "MotorInfoCollectInclude.h"


ADC_STRUCT              gADC;
UDC_STRUCT              gUDC;
TEMPLETURE_STRUCT       gTemperature;
LINE_CURRENT_STRUCT     gLineCur;
AI_STRUCT               gAI;
MT_STRUCT_Q24           gIMTQ24;
AMPTHETA_STRUCT         gIAmpTheta;
UVW_STRUCT              gIUVWQ12;
UVW_STRUCT_Q24          gIUVWQ24;
MT_STRUCT               gIMTQ12;
ALPHABETA_STRUCT        gIAlphBetaQ12;
ALPHABETA_STRUCT        gIAlphBeta;
MT_STRUCT               gIMTSetQ12;
IUVW_SAMPLING_STRUCT    gCurSamp;
CUR_EXCURSION_STRUCT    gExcursionInfo;


void GetCurExcursion(void)
{
    s16 m_ErrIu, m_ErrIv, m_ErrIw;

    #ifdef VFD_TEST_DEBUG
    gExcursionInfo.ErrIu = -128;
    gExcursionInfo.ErrIv = -128;
    gExcursionInfo.ErrIw = -128;
    return;
    #else
    if ((gMainStatus.RunStep != STATUS_LOW_POWER) &&
            (gMainStatus.RunStep != STATUS_STOP))
    #endif
    {
        gExcursionInfo.EnableCount = 0;
        return;
    }

    gExcursionInfo.EnableCount++;
    gExcursionInfo.EnableCount = (gExcursionInfo.EnableCount > 200) ? 200 : gExcursionInfo.EnableCount;
    if ((gExcursionInfo.EnableCount < 200))
    {
        gExcursionInfo.TotalIu = 0;
        gExcursionInfo.TotalIv = 0;
        gExcursionInfo.TotalIw = 0;
        gExcursionInfo.Count = 0;
        return;
    }
    gExcursionInfo.TotalIu += gExcursionInfo.Iu;
    gExcursionInfo.TotalIv += gExcursionInfo.Iv;
    gExcursionInfo.TotalIw += gExcursionInfo.Iw;
    gExcursionInfo.Count++;

    if (gExcursionInfo.Count >= 32)
    {
        m_ErrIu = gExcursionInfo.TotalIu >> 5;
        m_ErrIv = gExcursionInfo.TotalIv >> 5;
        m_ErrIw = gExcursionInfo.TotalIw >> 5;
        if (-32768 == m_ErrIu) //é˛ć?˘ĺçťĺ?šĺźćśćş˘ĺş
            m_ErrIu = -32767;
        if (-32768 == m_ErrIv)
            m_ErrIv = -32767;
        if (-32768 == m_ErrIw)
            m_ErrIw = -32767;
        gExcursionInfo.TotalIu = 0;
        gExcursionInfo.TotalIv = 0;
        gExcursionInfo.TotalIw = 0;
        gExcursionInfo.Count = 0;

//      gMainStatus.StatusWord.bit.RunEnable = 1;
        if ((abs(m_ErrIu) < 5120) && (abs(m_ErrIv) < 5120) && (abs(m_ErrIw) < 5120))
        {
            gExcursionInfo.ErrIu = m_ErrIu;
            gExcursionInfo.ErrIv = m_ErrIv;
            gExcursionInfo.ErrIw = m_ErrIw;
            gExcursionInfo.ErrCnt = 0;
        }
        else if ((gExcursionInfo.ErrCnt++) > 5) //čżçť­5ćŹĄéśćźć?ćľčżĺ¤§ćć?18ćé
        {
            gError.ErrorCode.all |= ERROR_CURRENT_CHECK;
            gExcursionInfo.ErrCnt = 0;
            gExcursionInfo.EnableCount = 0;
        }
    }
}
 uint32_t vbus_max;
 uint32_t vbus_min;
void GetUDCInfo(void)
{
    Uint m_uDC;

#if !(TEST_VF)
    m_uDC = ((Uint32)ADC_UDC * gUDC.Coff) >> 16;
#else
    m_uDC = ((Uint32)ADC_UDC * gUDC.Coff) >> 16;
//    m_uDC = ((Uint32)(2212L<<4) * gUDC.Coff)>>16;
#endif

    gUDC.uDC = ((gUDC.uDC + m_uDC) >> 1) + 1U ;
    
    gUDC.uDCFilter = gUDC.uDCFilter - (gUDC.uDCFilter >> 3) + (gUDC.uDC >> 3);
    
    if(gUDC.uDC > vbus_max) vbus_max = gUDC.uDC;
    if(gUDC.uDC < vbus_min) vbus_min = gUDC.uDC;
    
    gUDC.uDCShow = Filter32(gUDC.uDC, gUDC.uDCShow);
    gUDC.uDCBigFilter = Filter128(gUDC.uDC, gUDC.uDCBigFilter);
}

float g_Iu = 0;
float g_Iv = 0;
float g_Iw = 0;
float g_Div = 0;
extern int32 frq;
extern uint16_t gAdcRes_IgbtTemp;
void GetCurrentInfo(void)
{
    s32  m_Iu, m_Iv, m_Iw, m_TempT;

    //----------------------------------------------------------
    gExcursionInfo.Iu = (int16)(ADC_IU - (Uint)32768);
    m_Iu = (s32)gExcursionInfo.Iu - (s32)gExcursionInfo.ErrIu;
    gExcursionInfo.IuValue = m_Iu;
    gShortGnd.ShortCur = Filter32(m_Iu, gShortGnd.ShortCur);//wujian added for output phase lose and shortGND detect
    m_Iu = (m_Iu * gCurSamp.Coff) >> 3;
    m_Iu = __IQsat(m_Iu, C_MAX_PER, -C_MAX_PER);
    gIUVWQ24.U = m_Iu;

    //----------------------------------------------------------
    gExcursionInfo.Iv = (int16)(ADC_IV - (Uint)32768);
    m_Iv = (s32)gExcursionInfo.Iv - (s32)gExcursionInfo.ErrIv;
    gExcursionInfo.IvValue = m_Iv;
    m_Iv = (m_Iv * gCurSamp.Coff) >> 3;
    m_Iv = __IQsat(m_Iv, C_MAX_PER, -C_MAX_PER);
    gIUVWQ24.V = m_Iv;

    //----------------------------------------------------------
    gExcursionInfo.Iw = (int16)(ADC_IW - (Uint)32768);
    m_Iw = (s32)gExcursionInfo.Iw - (s32)gExcursionInfo.ErrIw;
    gExcursionInfo.IwValue = m_Iw;
    m_Iw = (m_Iw * gCurSamp.Coff) >> 3;
    m_Iw = __IQsat(m_Iw, C_MAX_PER, -C_MAX_PER);
    gIUVWQ24.W = m_Iw;
    //----------------------------------------------------------
//    gExcursionInfo.IwValue = - (gExcursionInfo.IuValue + gExcursionInfo.IvValue);
//    gIUVWQ24.W = - (gIUVWQ24.U + gIUVWQ24.V);
//    gIUVWQ24.W = __IQsat(gIUVWQ24.W,C_MAX_PER,-C_MAX_PER);


    //========wujian
    m_TempT = (gAdcRes_IgbtTemp << 4); //wujian
    gTemperature.TempAD = Filter16((m_TempT & 0xFFF0), gTemperature.TempAD);
}
