#ifndef MOTOR_INCLUDE_H
#define MOTOR_INCLUDE_H

#ifdef __cplusplus
extern "C" {
#endif


#include "arm_math.h"

#include "MotorCurrentTransform.h"
#include "MotorDefine.h"
#include "MotorInfoCollectInclude.h"
#include "MotorInvProtectInclude.h"
#include "MotorStructDefine.h"
#include "SubPrgInclude.h"
#include "MotorPwmInclude.h"
#include "MotorPublicCalInclude.h"
#include "MotorEncoder.h"
#include "MotorVCInclude.h"
#include "MotorPmsmParEst.h"
#include "MotorParaIDinclude.h"
#include "MotorDataExchange.h"
#include "MotorPmsmMain.h"
#include "MotorIPMSVC.h"
#include "MotorInfoCollectInclude.h"
#include "MotorVFInclude.h"
#include "MotorFlyingStart.h"
#define TEST_VF 1


extern Uint  const gInvVoltageInfo380T[];
extern Uint const gInvCurrentTable380T[];

extern Uint const gDeadBandTable[];
extern Uint const gDeadCompTable[];


extern BASE_COMMAND_STRUCT         gMainCmd;
extern RUN_STATUS_STRUCT           gMainStatus;
extern MOTOR_STRUCT                gMotorInfo;
extern MOTOR_EXTERN_STRUCT         gMotorExtInfo;
extern BASE_PAR_STRUCT             gBasePar;
extern MOTOR_POWER_TORQUE          gPowerTrq;
extern INV_STRUCT                  gInvInfo;
extern COM_PAR_INFO_STRUCT         gComPar;
extern MOTOR_DEBUG_DATA_RECEIVE_STRUCT     gTestDataReceive;
extern MOTOR_EXTERN_STRUCT		    gMotorExtPer;	//电机扩展信息（标么值表示）
extern SUB_COMMAND_UNION            gSubCommand;	//辅命令字结构
extern MAIN_COMMAND_EXTEND_UNION    gExtendCmd;     //主命令字扩展
extern CONTROL_MOTOR_TYPE_ENUM     gCtrMotorType;
extern Ulong gPeriod_BeforeEst;

#if 0
extern volatile uint16_t gAdcResult[ADC_12_CHNNS];
#else
extern uint32_t gAdcResult[];
#endif
extern Uint  gTuneFinishFlag;
extern void CalTorqueLimitPar(void);
extern void PrepareAsrPar(void);
extern void VcAsrControl(void);
extern void VCCsrControl(void);
extern void CalUVWVoltSet(int16 Phase);
extern void CalOutputPhase(void);
extern void OutPutPWM1(void);
extern void SetADCEndIsr(void (*p_mADCIsr)());
extern void ResetADCEndIsr(void);
extern void User_ADC_Init(void);
extern void VfSpeedControl(void);
#ifdef __cplusplus
}
#endif /* extern "C" */


#endif
