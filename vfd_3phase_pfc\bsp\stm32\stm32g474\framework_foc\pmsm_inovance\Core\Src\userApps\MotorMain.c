#include "GlobalIncludes.h"
#include "SystemDefine.h"
#include "MotorInclude.h"
#include "f_runSrc.h"
#include "f_interface.h"
#include "f_funcCode.h"
#include "f_runSrc.h"
#include "f_main.h"
#include "f_frqSrc.h"
#include "f_io.h"
#include "f_menu.h"
#include "f_posCtrl.h"
#include "f_interface.h"
#include "Scope.h"

Uint16 CurU[100] = {0};
Uint16 CurV[100] = {0};
Uint16 CurW[100] = {0};

Uint16 VolU[100] = {0};
Uint16 VolV[100] = {0};
Uint16 VolW[100] = {0};

Uint16 CurMaxU[5] = {0};
Uint16 CurMaxV[5] = {0};
Uint16 CurMaxW[5] = {0};

Uint16 VolMaxU[5] = {0};
Uint16 VolMaxV[5] = {0};
Uint16 VolMaxW[5] = {0};

extern BEFORE_RUN_PHASE_LOSE_STRUCT gBforeRunPhaseLose;
Uint16 CurFinalU, CurFinalV, CurFinalW, VolFinalUV, VolFinalUW, VolFinalVW;
int16  Vol_UV, Vol_UW, Vol_VW;


//-----------------------------------------------------------------------------------------------------------------
void PrepareParForRun(void);

//-----------------------------------------------------------------------------------------------------------------
void InitForMotorApp(void)
{
    DisableDrive();
    ResetADCEndIsr();


    gFluxWeak.CoefFlux = 5; //motorFc.vcPara.weakFlusCoef;               // 71 F2-19 同步机弱磁系数// hyj
    gPWM.OverModuleCoff = 110;// // A5-05    过调制系数  电流检测延时补偿更改为过调制系数  // hyj
    gPmFluxWeak.FluxWeakDepth = 5; // F2-23同步机输出电压饱和裕量// hyj

    gMainStatus.RunStep = STATUS_LOW_POWER; // 主步骤，首先进入的就是欠压状态
    gMainStatus.SubStep = 1; // 辅步骤
    gMainStatus.ParaCalTimes = 0;
    gError.LastErrorCode = gError.ErrorCode.all;
    gCBCProtect.EnableFlag = 1;
    gADC.ZeroCnt = 0;
    gADC.DelayApply = 0;
    gFcCal.FcBak = 50;
    gBasePar.FcSetApply = 50;
    gBrake.DelayClose = 0;
    gPWM.gPWMPrd = C_INIT_PRD;
    gPWM.gPWMPrdApply = gPWM.gPWMPrd;
    gDeadBand.DeadBand = C_MAX_DB;
    gMainStatus.StatusWord.all = 0;

// 矢量相关变量初始化
    gRotorSpeed.TransRatio = 1000;                  // 测速传动比固定值
    gPGData.QEPIndex = QEP_SELECT_NONE;
    gPGData.PGType = PG_TYPE_NULL;                  // 初始化为 null，
    gPWM.gZeroLengthPhase = ZERO_VECTOR_NONE;
    gAsr.KPLowCoff = 10;                            // 初始化Kp系数
    gTemperature.OverTempPoint = 95;                // 初始化过温点
    gEstBemf.BemfVoltDisPlayTotal = 0;

// 电机类型相关的初始化，默认是异步机电机
    gMotorInfo.MotorType = MOTOR_TYPE_IM;
    gMotorInfo.LastMotorType = MOTOR_NONE; // 保证进入主程序后能进行相关初始化
    gPGData.PGMode = 0;
    gIPMInitPos.Flag = 0;
    gUVCoff.Flag = 0;
    gUVCoff.RsTune = 0;
    gPmParEst.SynTunePGLoad = 0;
    gIPMInitPos.InitPWMTs = (20 * DSP_CLOCK);     //20us
    gPWM.PWMModle = MODLE_CPWM;
    gFanCtrl.RunCnt = 5000;
    gFanCtrl.ErrTimes = 0;
    gMainStatus.StatusWord.bit.OverLoadPreventState = 0;
    FlyingStartInitDeal();

    ParSend2Ms();
    ParSend05Ms(); //none
}

//-----------------------------------------------------------------------------------------------------------------
void Main05msMotor(void)
{
    gMainCmd.FreqSetApply = gMainCmd.FreqSet; //给定转速

    if (SYNC_SVC == gCtrMotorType)
    {
        gAsr.KPLowCoff = 10;
        gRotorSpeed.SpeedApply = (s16)gPmsmRotorPosEst.SvcRotorSpeed; //反馈转速

        if (STATUS_STOP != gMainStatus.RunStep)
        {
            VcAsrControl(); //转速环
        }
        else {}
        gMainCmd.FreqSyn = gRotorSpeed.SpeedApply; //反馈转速
        gMainCmd.FreqToFunc = gRotorSpeed.SpeedApply; //传递给功能的转速

        gPmsmRotorPosEst.CurFreqSetReal = (((s32)gMainCmd.FreqSyn * (s32)gBasePar.FullFreq01) >> 15) * 100; //记录进入低频运行之前的频率

    }
    else {}

    if (gMainCmd.Command.bit.Start)
    {
        if (ASYNC_VF == gCtrMotorType)
        {
            if (gMainStatus.RunStep != STATUS_GET_PAR)
            {
                VfSpeedControl();
            }
        }
    }

    OutputLoseAdd();

}
Uint16 outVoltageDisp;
//-----------------------------------------------------------------------------------------------------------------
#include "testApp.h"
void Main2msMotorA(void)
{
    Uint32 i = 0, tempU = 0, tempV = 0, tempW = 0;

    for (i = 0; i <= 4; i++)
    {
        tempU += CurMaxU[i];
        tempV += CurMaxV[i];
        tempW += CurMaxW[i];
    }

    tempU = (((int32)tempU * gMotorInfo.Current) >> 12) / 7;
    tempV = (((int32)tempV * gMotorInfo.Current) >> 12) / 7;
    tempW = (((int32)tempW * gMotorInfo.Current) >> 12) / 7;

    CurFinalU = Filter16(tempU, CurFinalU);
    CurFinalV = Filter16(tempV, CurFinalV);
    CurFinalW = Filter16(tempW, CurFinalW);



    tempU = 0;
    tempV = 0;
    tempW = 0;

    for (i = 0; i <= 4; i++)
    {
        tempU += VolMaxU[i];
        tempV += VolMaxV[i];
        tempW += VolMaxW[i];
    }
    tempU = (((int32)tempU * (((u32)gUDC.uDCBigFilter * 2320L) >> 15)) >> 12) / 7;
    tempV = (((int32)tempV * (((u32)gUDC.uDCBigFilter * 2320L) >> 15)) >> 12) / 7;
    tempW = (((int32)tempW * (((u32)gUDC.uDCBigFilter * 2320L) >> 15)) >> 12) / 7;
    VolFinalUV = Filter16(tempU, VolFinalUV);
    VolFinalUW = Filter16(tempV, VolFinalUW);
    VolFinalVW = Filter16(tempW, VolFinalVW);

    int VoltDisplay = __IQsat(gOutVolt.VoltApply, gOutVolt.MaxOutVolt, 0);
    outVoltageDisp = ((int32)VoltDisplay * gMotorInfo.Votage + (1 << 11)) >> 12;

    gVoltUVW.Uout  = outVoltageDisp * 10;
#if !(TEST_VF)
    //从控制板获取参数
    ParGet05Ms();

#endif

    ParGet2Ms();

    gMainCmd.Command.all            =   dspMainCmd.all  ;
    gMainCmd.FreqSet                =   frq2Core;

//    if(gMainCmd.Command.bit.Start)
//    {
//      AccDecFrqCalc1(1500,1500,0);
//    }

    if (0 == gMainCmd.Command.bit.Start)
    {
        SystemParChg2Ms();
        SystemParChg05Ms(); // 运行时不转换的参数

        ChangeMotorPar(); // 电机参数转换， 运行不转换
    }
    RunStateParChg2Ms();
}

//-----------------------------------------------------------------------------------------------------------------
void Main2msMotorB(void)
{
    switch (gCtrMotorType)
    {
    case ASYNC_SVC:
    {
        break;
    }
    case ASYNC_VF:
    {
        gPhase.OutVoltPhaseCom = 0;
        break;
    }
    case ASYNC_FVC:
    {
        break;
    }
    case SYNC_SVC:
    {
        gPhase.OutVoltPhaseCom = 0;
        break;
    }
    case DC_CONTROL:
    {
        gMainCmd.FreqSyn = 0;
        RunCaseDcBrake();
        gOutVolt.VoltPhaseApply = 0;        // 考虑到同步机，输出电对准转子磁极                                      // 定子磁链就会诟方向上?
        gMainCmd.FreqToFunc = 0;

        break;
    }
    case RUN_SYNC_TUNE:
    {
        if (gBasePar.FcSetApply > C_DOUBLE_ACR_MAX_FC)
        {
            gImAcrQ24.KP = (s32)gPmParEst.IdKp * gBasePar.FcSetApply * gPmParEst.IsKpK / 1000;         // 辨识时减小Kp,一次发波减小Kp和Ki
            gItAcrQ24.KP = (s32)gPmParEst.IqKp * gBasePar.FcSetApply * gPmParEst.IsKpK / 1000;
            gImAcrQ24.KI = (s32)gPmParEst.IdKi * gPmParEst.IsKiK / 10;
            gItAcrQ24.KI = (s32)gPmParEst.IqKi * gPmParEst.IsKiK / 10;
        }
        else
        {
            gImAcrQ24.KP = (s32)gPmParEst.IdKp * gBasePar.FcSetApply * gPmParEst.IsKpK / 500;         // 参数辨识时减小Kp和Ki
            gItAcrQ24.KP = (s32)gPmParEst.IqKp * gBasePar.FcSetApply * gPmParEst.IsKpK / 500;
            gImAcrQ24.KI = (s32)gPmParEst.IdKi * gPmParEst.IsKiK / 10;
            gItAcrQ24.KI = (s32)gPmParEst.IqKi * gPmParEst.IsKiK / 10;
        }
        gImAcrQ24.KP = Min(gImAcrQ24.KP, 8000);
        gItAcrQ24.KP = Min(gItAcrQ24.KP, 8000);
        break;
        ///////////////////////////////////////////////////////
        // if(gBasePar.FcSetApply > C_DOUBLE_ACR_MAX_FC)
        // {
        //     gImAcrQ24.KP = (s32)gPmParEst.IdKp * gBasePar.FcSetApply / 100;
        //     gItAcrQ24.KP = (s32)gPmParEst.IqKp * gBasePar.FcSetApply / 100;
        //     gImAcrQ24.KI = (s32)gPmParEst.IdKi ;
        //     gItAcrQ24.KI = (s32)gPmParEst.IqKi ;
        // }
        // else
        // {
        //  gImAcrQ24.KP = (s32)gPmParEst.IdKp * gBasePar.FcSetApply / 50;
        //     gItAcrQ24.KP = (s32)gPmParEst.IqKp * gBasePar.FcSetApply / 50;
        //     gImAcrQ24.KI = (s32)gPmParEst.IdKi ;
        //     gItAcrQ24.KI = (s32)gPmParEst.IqKi ;
        // }
        // gImAcrQ24.KP = Min(gImAcrQ24.KP,8000);
        // gItAcrQ24.KP = Min(gItAcrQ24.KP,8000);
        // break;
    }
    default:
    {
        gMainCmd.FreqSyn = 0;
        gMainCmd.FreqToFunc = 0;
        break;
    }
    }

    CalCarrierWaveFreq(); // 最终的载频确定函数

    //控制程序的状态机
    switch (gMainStatus.RunStep)
    {
    case STATUS_RUN: //5:  运行状态，区分VF/FVC/SVC运行
    {
        RunCaseRun2Ms();
        break;
    }
    case STATUS_STOP: //3
    {
        RunCaseStop();
        break;
    }
    case STATUS_IPM_INIT_POS:                   //同步机初始位置角检测阶段
    {
       RunCaseIpmInitPos();   
       break;
    }
		
    case STATUS_GET_PAR: //2:  参数辨识状态，移到0.5ms时要同时修改参数传递
    {
        RunCaseGetPar();
        break;
    }
    case STATUS_LOW_POWER: //1  上电缓冲状态/欠压状态
    {
        RunCaseLowPower();
        break;
    }
//-------------------wuajian added for shortGND detect and phase lose
    case STATUS_SHORT_GND:                      //上电对地短路判断状态
        RunCaseShortGnd();
        break;

    case STATUS_BEFORE_RUN_DETECT:
        OutputPhaseLoseAndShortGndDetect();
        break;

    case STATUS_FLYING_START:
        RunCaseFlyingStart();
        break;
//-------------------------------------------------------------------
    default:
    {
        gMainStatus.RunStep = STATUS_STOP; // 上电后首先进入的因该是欠压状态
        break;
    }
    }
}

//-----------------------------------------------------------------------------------------------------------------
void Main2msMotorC(void)
{
    gRotorSpeed.SpeedBigFilter = Filter(gRotorSpeed.SpeedBigFilter, gRotorSpeed.SpeedApply, 1024);
    //变频器身检测和保护
    InvDeviceControl();

}

extern void boost_cutoff_pwm(void);
//========================
void UdcDetect(void)//wujian
{
    if (gUDC.uDCBigFilter < gInvInfo.InvLowUDC) //wujian 欠压判断,使用大滤波电压
        // if(gUDC.uDC < gInvInfo.InvLowUDC) //欠压判断,使用大滤波电压
    {
        DisableDrive();

        if (STATUS_GET_PAR == gMainStatus.RunStep)  //避免参数辨识过程中欠压导致寄存器配置修改后没有恢复
        {
            EndOfParIdentify();
        }
        gMainStatus.RunStep = STATUS_LOW_POWER;
        gMainStatus.SubStep = 0;
        gError.ErrorCode.all |= ERROR_LOW_UDC;
        gMainStatus.StatusWord.bit.LowUDC = 0;
    }
    if ((gUDC.uDCFilter > gInvInfo.InvUpUDC) || (gUDC.uDC > 55125)) //55125对应860V
    {
        DisableDrive(); //停机也允许报过压，提醒用户输入电压过高
        gError.ErrorCode.all |= ERROR_OVER_UDC;
        if (gUDC.uDCFilter > gInvInfo.InvUpUDC)
        {
            gError.ErrorInfo[0].bit.Fault2 = 1; //软件过压
        }
        else
        {
            gError.ErrorInfo[0].bit.Fault2 = 2;
        }
#if(AIRCOMPRESSOR == 1)
        if ((gMainCmd.RestarCnt <= 3) && (gOverLoad.OverLoadPreventEnable == 1))
        {
            gMainStatus.StatusWord.bit.OverLoadPreventState = 2;
        }
#endif
    }
}

//-----------------------------------------------------------------------------------------------------------------
void Main2msMotorD(void)
{
    GetCurExcursion(); //零漂检测

    OutputPhaseLoseDetect();
    LoadLoseDetect();                   //掉载处理
    UdcDetect();


    //把实时数据传送给控制板
    ParSend2Ms();
    ParSend05Ms();
}

//-----------------------------------------------------------------------------------------------------------------
void PrepareParForRun(void)
{
//#if TEST_VF
//  gCtrMotorType = ASYNC_VF; //异步电机
//  gCtrMotorType = SYNC_SVC; //异步电机
//#else
//    gCtrMotorType = (CONTROL_MOTOR_TYPE_ENUM)(gMainCmd.Command.bit.ControlMode + 10);
//#endif

    if (MOTOR_TYPE_IM == gMotorInfo.MotorType) //ACIM
    {
        gCtrMotorType = (CONTROL_MOTOR_TYPE_ENUM)(gMainCmd.Command.bit.ControlMode);
    }
    else //PMSM
    {
        gCtrMotorType = (CONTROL_MOTOR_TYPE_ENUM)(gMainCmd.Command.bit.ControlMode + 10);
    }

    gPmParEst.SynTunePGLoad = 0;

    // 公共变量初始化
    gMainStatus.StatusWord.bit.StartStop = 0;
    gMainStatus.StatusWord.bit.SpeedSearchOver = 0;

    gMainStatus.PrgStatus.all = 0;
    gMainStatus.PrgStatus.bit.ACRDisable = 1;
    gGetParVarable.StatusWord = TUNE_INITIAL;
    gVarAvr.UDCFilter = gInvInfo.BaseUdc;
    gMainCmd.FreqSyn = 0;
    gMainCmd.FreqReal = 0;
    gOutVolt.Volt = 0;
    gOutVolt.VoltApply = 0;
    gRatio = 0;
    gCurSamp.U = 0;
    gCurSamp.V = 0;
    gCurSamp.W = 0;
    gCurSamp.UErr = 600L << 12;
    gCurSamp.VErr = 600L << 12;
    gIUVWQ24.U = 0;
    gIUVWQ24.V = 0;
    gIUVWQ24.W = 0;
    gIUVWQ12.U = 0;
    gIUVWQ12.V = 0;
    gIUVWQ12.W = 0;
    gLineCur.CurPerShow = 0;
    gLineCur.CurTorque  = 0;
    gLineCur.CurBaseInv = 0;
    gLineCur.CurPer = 0;
    gLineCur.CurPerFilter = 0;
    gLineCur.ImTotal = 0;
    gLineCur.ItTotal = 0;
    gLineCur.CurCnt = 0;
    gIMTQ12.M = 0;
    gIMTQ12.T = 0;
    gIMTQ24.M = 0;
    gIMTQ24.T = 0;
//  gDCBrake.Time = 0;
    gPWM.gZeroLengthPhase = ZERO_VECTOR_NONE;
    gIAmpTheta.ThetaFilter = gIAmpTheta.Theta;
    gMainStatus.StatusWord.bit.RunStart = 0;

// Vf 相关都初始化
    if (gMainCmd.Command.bit.ControlMode == IDC_VF_CTL)
    {
//      VfVarInitiate();
    }

    if (gMainCmd.Command.bit.ControlMode != IDC_VF_CTL)
    {
        ResetParForVC();
        ResetParForPmsmSvc();
    }
    //////////////////-----
    if (gOverLoad.OverLoadPreventEnable == 1)   //10s后标志位清零，等待重启
    {
        static uint16_t m_WaitTime = 0;
        if (gMainStatus.StatusWord.bit.OverLoadPreventState == 2)
        {
            m_WaitTime ++;
            if (m_WaitTime > 5000)   //10s
            {
                gMainStatus.StatusWord.bit.OverLoadPreventState = 0;
                m_WaitTime = 0;
// #if(AIRCOMPRESSOR == 1)
//              gMainCmd.RestarCnt ++;
// #endif
            }
        }
        else
        {
            m_WaitTime = 0;
        }
    }

}

//-----------------------------------------------------------------------------------------------------------------
// #include "debug_test.h"
void RunCaseRun2Ms(void)
{
    s32 Imtset_M;
    
    if ((gMainCmd.Command.bit.Start == 0) || (gBrake.ErrCode != 0))          // 结束运行
    {
        DisableDrive();
        gMainStatus.RunStep = STATUS_STOP;
        ResetADCEndIsr();
        RunCaseStop();
        return;
    }
    gMainStatus.StatusWord.bit.StartStop = 1;

    switch (gCtrMotorType)
    {
    case ASYNC_VF:
    {
        break;
    }
    case ASYNC_SVC:
    {
        break;
    }
    case ASYNC_FVC:
    {
        break;
    }
    case SYNC_SVC:
    {
        CalTorqueLimitPar(); //转矩限幅
        PrepareAsrPar(); //
        PrepPmsmCsrPrar();
        Imtset_M = PmsmFwcAdjMethod();
        Imtset_M = Imtset_M << 12;

        PmsmSvcCalImForLowSpeed();
        Imtset_M += (int32)gPmsmRotorPosEst.SvcIdSetForLowSpeed << 12;
        gIMTSet.M = Imtset_M; //Q24格式
        break;
    }
    case DC_CONTROL:
    {
        break;
    }
    case RUN_SYNC_TUNE:
    {
        break;
    }
    }

}
/************************************************************
    鍒囨崲鍒板仠鏈虹姸鎬�(鍏�鐢ㄥ瓙鍑芥暟)
************************************************************/
void TurnToStopStatus(void)
{
    DisableDrive();
    gMainStatus.RunStep = STATUS_STOP;
    gMainStatus.SubStep = 1;
}
//-----------------------------------------------------------------------------------------------------------------
extern u16 ChargePumpFlag;//wujian 自举电容充电 至少4Ms
extern uint8_t gFlyBreakStartFlag;
extern uint8_t gShortgnd_cmdinit_flag;
void RunCaseStop(void)
{
    Ulong m_Period;

    ResetADCEndIsr(); // 复位主中断
    DisableDrive(); // 封波
    //SynInitPosDetSetPwm(7);

    gMainCmd.FreqSet = 0;

    PrepareParForRun();

    // gMainStatus.StatusWord.bit.ShortGndOver = 1; wujian for shortGND detect

    if ((gMainCmd.Command.bit.Start == 0) && (1 == gTuneFinishFlag))
    {
        gTuneFinishFlag = 0; // 给了停机命令，但是辨识已经结束时，清零该标志
        dspSubStatus.bit.uiTuneStep = 0;
    }
//-----------------------wujian add for phase lose befor start--------
    if (gMainCmd.Command.bit.Start == 0)
    {
        gBforeRunPhaseLose.CheckOverFlag = 0;
        gBforeRunPhaseLose.CheckFirstFlag = 0;
        gFlyBreakStartFlag = 0;
    }
    
    if(gShortgnd_cmdinit_flag == 0) return;
    
    //判断是否需要对地短路检测
    if ((1 == gExtendCmd.bit.ShortGnd) && (gMainStatus.StatusWord.bit.ShortGndOver == 0)) //wujian 主命令扩展上电检测对地
    {
        if(vfd.bit.close_detect)
        {
            extern u16 ShortGndCheckOk;
            gMainStatus.StatusWord.bit.ShortGndOver = 1;
            ShortGndCheckOk = 1;
        }
        else
        {
            ChargePumpFlag = 0; //wujian 自举电容充电 至少4Ms
            gMainStatus.RunStep = STATUS_SHORT_GND;
            gMainStatus.SubStep = 1;        // 重新进行对地短路检测
            gPeriod_BeforeEst = __HAL_TIM_GET_AUTORELOAD(&htim1);
            // System_Start_InvPWM();
            if (gBforeRunPhaseLose.CheckFirstFlag == 0)
            {
                //          EALLOW;
                //          EPwm1Regs.AQCSFRC.all   = 0x0A;
                //          EPwm2Regs.AQCSFRC.all   = 0x0A;
                //          EPwm3Regs.AQCSFRC.all   = 0x0A;
                //          EDIS;

                // TMR1->CCM1 = 0x4848;// Output keep PWM1 mode (CH2/CH1)
                // TMR1->CCM2 = 0x6848;//
                // TMR1->CCE  = 0x3AAA;//
                gBforeRunPhaseLose.CheckFirstFlag = 1;
            }
        }
        return;
    }
//    else
//    {
//        gMainStatus.StatusWord.bit.ShortGndOver = 1;
//    }
//--------------------------------------------------------------------
//判断是否需要起动电机
    if (gMainCmd.Command.bit.Start == 1)
    {
#if 1
        if (TUNE_NULL != gGetParVarable.TuneType)
        {
            gPeriod_BeforeEst = __HAL_TIM_GET_AUTORELOAD(&htim1);
            gMainStatus.RunStep = STATUS_GET_PAR;
            PrepareParForTune();
            return;
        }
#endif
//-----------------------wujian add for phase lose befor start--------
        if (((gSubCommand.bit.OutputLostBeforeRun == 1) ||
                (gExtendCmd.bit.ShortGndBeforeRun == 1)) &&
                (gBforeRunPhaseLose.CheckOverFlag == 0) &&
                (vfd.bit.close_detect == 0))
        {
            gMainStatus.RunStep = STATUS_BEFORE_RUN_DETECT;
            gMainStatus.SubStep = 1;
            gPeriod_BeforeEst = __HAL_TIM_GET_AUTORELOAD(&htim1);
            ChargePumpFlag = 0; //wujian 自举电容充电 至少4Ms
            if (gBforeRunPhaseLose.CheckFirstFlag == 0)
            {
                gBforeRunPhaseLose.CheckFirstFlag = 1;
            }
            return;
        }
//--------------------------------------------------------------------
        if (gError.ErrorCode.all != 0)
        {
            return;
        }
        if (((gExtendCmd.bit.SpeedSearch == 1) && (SYNC_SVC == gCtrMotorType)) && (gFlyingStart.Flag == 0))
        {
            gMainStatus.RunStep = STATUS_FLYING_START;   // 杞�閫熻窡韪�璧峰姩鐘舵€�
            gPeriod_BeforeEst = __HAL_TIM_GET_AUTORELOAD(&htim1);
            gFlyingStart.Step = 0;
            gMainStatus.SubStep = 1;
            gFlyingStart.PhaseLoseCnt = 0;
            return;
        }
        //同步机识别磁极初始位置角阶段
// #define FVC_INIT_TEST_CONDITION  ((0 == gRotorSpeed.SpeedEncoder) && (gPGData.PGType == PG_TYPE_ABZ))

//     	if(((gIPMInitPos.Flag == 0) &&(1 != gIPMInitPos.InitTestFlag) &&
//             (FVC_INIT_TEST_CONDITION || (SYNC_SVC == gCtrMotorType))&&(SYNC_VF != gCtrMotorType))||
// 			((gIPMInitPos.Flag == 2)&&(SYNC_SVC == gCtrMotorType)))

        if(((gIPMInitPos.Flag == 0) &&(1 != gIPMInitPos.InitTestFlag) &&
            ((SYNC_SVC == gCtrMotorType))&&(SYNC_VF != gCtrMotorType))||
			((gIPMInitPos.Flag == 2)&&(SYNC_SVC == gCtrMotorType)))
		{
			gMainStatus.RunStep = STATUS_IPM_INIT_POS;
			gMainStatus.SubStep = 1;
            gIPMInitPos.Step = 0;
            gIPMInitPos.OkFlag = 1;
            return;
		}
        if (SYNC_SVC == gCtrMotorType)
        {
            gMainStatus.StatusWord.bit.RunStart = 1;   //传给功能的速度指令
        }
        if((SYNC_SVC == gCtrMotorType)
            &&(0 == gIPMInitPos.InitTestFlag))
        {
            gIPMInitPos.Flag = 0;  
        }
        FlyingStartInitDeal();
        gMainStatus.RunStep = STATUS_RUN;
        gMainStatus.PrgStatus.all = 0;
        gMainStatus.SubStep = 1;

        gBrake.DelayClose = 0;
        EnableDrive();
        SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1E); // PWM1P输出（√�?
        SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC1NE); // PWM1N输出（√�?
        SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2E); // PWM2P输出（√�?
        SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC2NE); // PWM2N输出（√�?
        SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3E); // PWM3P输出（√�?
        SET_BIT((&htim1)->Instance->CCER, TIM_CCER_CC3NE); // PWM3N输出（√�?
        RunCaseRun2Ms();
        
    }

}

//-----------------------------------------------------------------------------------------------------------------
void ADCEndIsr(void)
{
    Uint m_TIM1_CountDir;

    DispVolInit();

    if (STATUS_RUN == gMainStatus.RunStep)
    {
        DispVol();
    }

    if (!__HAL_TIM_IS_TIM_COUNTING_DOWN(&htim1))
    {
        m_TIM1_CountDir = 1; // upCount
    }
    else
    {
        m_TIM1_CountDir = 0; // downCount
    }

    if (gBasePar.FcSetApply > C_DOUBLE_ACR_MAX_FC)
    {

        /* 重新赋值与载频相关的系数 */
        if (m_TIM1_CountDir == 1) //向上计数时
        {
            PWMZeroIsr();
            OutPutVolt();
        }
        else
        {
            if (gPmsmRotorPosEst.FcCoff == 200)
            {
                PWMZeroIsr();
                PWMPeriodIsr();
                return;
            }
            PWMPeriodIsr();

        }

        gPmsmRotorPosEst.FcCoff = 400;
        gMainCmd.StepCoeff      = 2;

    }
    else if ((gMainCmd.StepCoeff == 2) && (m_TIM1_CountDir == 0) //从单次发波变为两次发波的过度阶段
             && (gBasePar.FcSetApply <= C_DOUBLE_ACR_MAX_FC))
    {
        PWMPeriodIsr();
        gPmsmRotorPosEst.FcCoff = 200;
        gMainCmd.StepCoeff      = 1;
    }
    else
    {
        gPmsmRotorPosEst.FcCoff = 200;
        gMainCmd.StepCoeff      = 1;
        PWMZeroIsr();
        PWMPeriodIsr();
    }
}

//-----------------------------------------------------------------------------------------------------------------
void PWMPeriodIsr(void)
{
#if 1
    switch (gCtrMotorType)
    {
    case ASYNC_VF:
    {
        break;
    }
    case ASYNC_SVC:
    {
        break;
    }
    case ASYNC_FVC:
    {
        break;
    }
    case SYNC_SVC:
    {
        if (STATUS_RUN != gMainStatus.RunStep)  // 停机后不能执行磁极位置辨识程序，会导致下次启动时的磁极位置改变
        {
            break;
        }
        PmsmSvcCtrl();       /*同步机开环矢量控制速度和位置估算*/
        gPhase.IMPhaseApply = gPmsmRotorPosEst.SvcRotorPos;
        gPhase.IMPhase = gPmsmRotorPosEst.SvcRotorPos >> 16;
        VCCsrControl();
        break;
    }
    case DC_CONTROL:
    {
        break;
    }
    case RUN_SYNC_TUNE:
    {
        VCCsrControl();
        break;
    }
    default:
    {
        break;
    }
    }
    gPhase.OutPhase = gPhase.IMPhase + gOutVolt.VoltPhaseApply + gPhase.OutVoltPhaseCom;
    CalRatioFromVot();
    SoftPWMProcess();
    OutPutPWM1();
    OutPutVolt();
#endif
}

int32 IU_Q12, IV_Q12, IW_Q12;
Uint16 ticker = 0;
Uint16 MaxIndex = 0;
Uint16 MaxU = 0, MaxV = 0, Maxw = 0;
Uint16 ticker1 = 0;
Uint16 MaxIndex1 = 0;
Uint16 Vol_MaxU = 0, Vol_MaxV = 0, Vol_Maxw = 0;

__IO test_adc = 0;
//-----------------------------------------------------------------------------------------------------------------
void PWMZeroIsr(void)
{
    IU_Q12 = gIUVWQ24.U >> 12;
    IV_Q12 = gIUVWQ24.V >> 12;
    IW_Q12 = gIUVWQ24.W >> 12;

    CurU[ticker] = (Uint16)abs(IU_Q12);
    CurV[ticker] = (Uint16)abs(IV_Q12);
    CurW[ticker] = (Uint16)abs(IW_Q12);

    if (CurU[ticker] > MaxU)     MaxU = CurU[ticker];
    if (CurV[ticker] > MaxV)     MaxV = CurV[ticker];
    if (CurW[ticker] > Maxw)     Maxw = CurW[ticker];
    if((MaxU > 0x200) || (MaxV > 0x200) || (Maxw > 0x200))
    {
        test_adc++;
    }
        
    
    ticker++;
    if (ticker >= 100)
    {
        ticker = 0;
        CurMaxU[MaxIndex] = MaxU;
        CurMaxV[MaxIndex] = MaxV;
        CurMaxW[MaxIndex] = Maxw;
        MaxU = 0;
        MaxV = 0;
        Maxw = 0;
        MaxIndex++;
        if (MaxIndex >= 5) MaxIndex = 0;
    }

    Vol_UV = gVoltUVW.U - gVoltUVW.V; //Q12
    Vol_UW = gVoltUVW.U - gVoltUVW.W; //Q12
    Vol_VW = gVoltUVW.V - gVoltUVW.W; //Q12

    VolU[ticker1] = (Uint16)abs(Vol_UV);
    VolV[ticker1] = (Uint16)abs(Vol_UW);
    VolW[ticker1] = (Uint16)abs(Vol_VW);

    if (VolU[ticker1] >= Vol_MaxU)   Vol_MaxU = VolU[ticker1];
    if (VolV[ticker1] >= Vol_MaxV)   Vol_MaxV = VolV[ticker1];
    if (VolW[ticker1] >= Vol_Maxw)   Vol_Maxw = VolW[ticker1];

    ticker1++;

    if (ticker1 >= 100)
    {
        ticker1 = 0;

        VolMaxU[MaxIndex1] = Vol_MaxU;
        VolMaxV[MaxIndex1] = Vol_MaxV;
        VolMaxW[MaxIndex1] = Vol_Maxw;

        Vol_MaxU = 0;
        Vol_MaxV = 0;
        Vol_Maxw = 0;

        MaxIndex1++;

        if (MaxIndex1 >= 5)    MaxIndex1 = 0;
    }

    GetUDCInfo();      // 获取母线电压采样数据
    GetCurrentInfo();  // 获取采样电流, 以及温度、母线电压的采样
    CalOutputPhase();  // 计算输出相位
    ChangeCurrent();   // 计算各种场合下的电流量
}


//-----------------------------------------------------------------------------------------------------------------
void ResetADCEndIsr(void)
{
    gMainCmd.pADCIsr = ADCEndIsr;
}

//-----------------------------------------------------------------------------------------------------------------
void SetADCEndIsr(void (*p_mADCIsr)())
{
    gMainCmd.pADCIsr = p_mADCIsr;
}

