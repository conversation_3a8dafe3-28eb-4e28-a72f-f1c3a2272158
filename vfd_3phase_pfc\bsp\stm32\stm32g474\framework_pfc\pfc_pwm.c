
/***************************************************************************

Copyright (C), 2022-2022, BSM Tech. Co., Ltd.

* @file           pfc_pwm.c
* <AUTHOR> @version        V0.0.1
* @date           2022-09-06
* @brief          this is PFC PWM

History:          // Revision Records

       <Author>             <time>         <version >               <desc>



***************************************************************************/


#include "pfc_pwm.h"
#include "pfc_sdpll.h"
#include "pfc_dqctrl.h"
#include "uapp.h"
#include "app_ad.h"
extern TIM_HandleTypeDef htim8;
extern ADC_HandleTypeDef hadc1;


/* Private defines -----------------------------------------------------------*/
#define TIMxCCER_MASK_CH123        ((uint16_t)  (LL_TIM_CHANNEL_CH1|LL_TIM_CHANNEL_CH1N|\
                                                 LL_TIM_CHANNEL_CH2|LL_TIM_CHANNEL_CH2N|\
                                                 LL_TIM_CHANNEL_CH3|LL_TIM_CHANNEL_CH3N))

uint32_t Tim8_Freq = 0;
void MX_TIM8_Init(void)
{
/* USER CODE BEGIN TIM1_Init 0 */

  /* USER CODE END TIM1_Init 0 */

  TIM_ClockConfigTypeDef sClockSourceConfig = {0};
  TIM_MasterConfigTypeDef sMasterConfig = {0};
  TIMEx_BreakInputConfigTypeDef sBreakInputConfig = {0};
  TIM_OC_InitTypeDef sConfigOC = {0};
  TIM_BreakDeadTimeConfigTypeDef sBreakDeadTimeConfig = {0};

  /* USER CODE BEGIN TIM1_Init 1 */

  /* USER CODE END TIM1_Init 1 */
  htim8.Instance = TIM8;
  htim8.Init.Prescaler = 0;
  htim8.Init.CounterMode = TIM_COUNTERMODE_CENTERALIGNED1;
  htim8.Init.Period = USER_PFC_PWM_ARR;//(170000UL >> 1) / 20;//(uint16_t)USER_PWM_FREQ_kHz;
  
  Tim8_Freq = 85000000/htim8.Init.Period;
  htim8.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim8.Init.RepetitionCounter = 1;
  htim8.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
  if (HAL_TIM_Base_Init(&htim8) != HAL_OK)
  {
    Error_Handler();
  }
  sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
  if (HAL_TIM_ConfigClockSource(&htim8, &sClockSourceConfig) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_TIM_PWM_Init(&htim8) != HAL_OK)
  {
    Error_Handler();
  }
  sMasterConfig.MasterOutputTrigger = TIM_TRGO_OC4REF;//TIM_TRGO_OC4REF;//TIM_TRGO_UPDATE;
  sMasterConfig.MasterOutputTrigger2 = TIM_TRGO2_RESET;
  sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
  if (HAL_TIMEx_MasterConfigSynchronization(&htim8, &sMasterConfig) != HAL_OK)
  {
    Error_Handler();
  }
  sBreakInputConfig.Source = TIM_BREAKINPUTSOURCE_BKIN;
  sBreakInputConfig.Enable = TIM_BREAKINPUTSOURCE_ENABLE;
  sBreakInputConfig.Polarity = TIM_BREAKINPUTSOURCE_POLARITY_HIGH;
  if (HAL_TIMEx_ConfigBreakInput(&htim8, TIM_BREAKINPUT_BRK, &sBreakInputConfig) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_TIMEx_ConfigBreakInput(&htim8, TIM_BREAKINPUT_BRK2, &sBreakInputConfig) != HAL_OK)
  {
    Error_Handler();
  }
  sConfigOC.OCMode = TIM_OCMODE_PWM1;//TIM_OCMODE_PWM2;
  sConfigOC.Pulse = 0;
  sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
  sConfigOC.OCNPolarity = TIM_OCNPOLARITY_HIGH;
  sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
  sConfigOC.OCIdleState = TIM_OCIDLESTATE_RESET;
  sConfigOC.OCNIdleState = TIM_OCNIDLESTATE_RESET;
  if (HAL_TIM_PWM_ConfigChannel(&htim8, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_TIM_PWM_ConfigChannel(&htim8, &sConfigOC, TIM_CHANNEL_2) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_TIM_PWM_ConfigChannel(&htim8, &sConfigOC, TIM_CHANNEL_3) != HAL_OK)
  {
    Error_Handler();
  }
	
  sConfigOC.OCMode = TIM_OCMODE_PWM2;
  if (HAL_TIM_PWM_ConfigChannel(&htim8, &sConfigOC, TIM_CHANNEL_4) != HAL_OK)
  {
    Error_Handler();
  }
	
  sBreakDeadTimeConfig.OffStateRunMode = TIM_OSSR_ENABLE;
  sBreakDeadTimeConfig.OffStateIDLEMode = TIM_OSSI_ENABLE;
  sBreakDeadTimeConfig.LockLevel = TIM_LOCKLEVEL_OFF;
  
  /*    DTGF[7:0]  170M   t= 5.9ns
  DTGF[7:5] =   0xx  => DTF = [7:0] x t                 [6:0] max127
                10x     DTF = (64+[5:0])x 2t            [5:0] max63
                110     DTF = (32+[4:0])x 8t            [4:0] max31
                111     DTF = (32+[4:0])x 16t           [4:0] max31

  need 1us ,    choose 0xx [6:0]set to 85 => 85 x 5.9ns = 0.5us
  need 1.5us ,  choose 10x [5:0]set to  0 => (64+0) x 5.9ns x 2 = 0.75us
  need 2us ,    choose 10x [5:0]set to 21 => (64+21) x 5.9ns x 2 = 1us
  need 3us ,    choose 110 [4:0]set to 0  => (32+0) x 5.9ns x 8 = 1.5us
                choose 110 [4:0]set to 10  => (32+0) x 5.9ns x 8 = 2.0us
  */

  sBreakDeadTimeConfig.DeadTime = (0 << 7) +
                                  (0 << 6) +
                                  (0 << 5) +  85; //0.5us


  sBreakDeadTimeConfig.BreakState  = TIM_BREAK_DISABLE;//TIM_BREAK_ENABLE;
  sBreakDeadTimeConfig.BreakPolarity = TIM_BREAKPOLARITY_LOW;
  sBreakDeadTimeConfig.BreakFilter = 0;
  sBreakDeadTimeConfig.BreakAFMode = TIM_BREAK_AFMODE_INPUT;

  sBreakDeadTimeConfig.Break2State = TIM_BREAK2_DISABLE;
  sBreakDeadTimeConfig.Break2Polarity = TIM_BREAK2POLARITY_LOW;
  sBreakDeadTimeConfig.Break2Filter = 0;
  sBreakDeadTimeConfig.Break2AFMode = TIM_BREAK_AFMODE_INPUT;
  sBreakDeadTimeConfig.AutomaticOutput = TIM_AUTOMATICOUTPUT_DISABLE;
  if (HAL_TIMEx_ConfigBreakDeadTime(&htim8, &sBreakDeadTimeConfig) != HAL_OK)
  {
    Error_Handler();
  }
  
  LL_TIM_DisableIT_BRK(TIM8);
  LL_TIM_ClearFlag_BRK2(TIM8);
  LL_TIM_ClearFlag_BRK(TIM8);
  /* USER CODE BEGIN TIM1_Init 2 */

  /* USER CODE END TIM1_Init 2 */
  HAL_TIM_MspPostInit(&htim8);
  
  LL_TIM_OC_SetCompareCH4(TIM8, (USER_PFC_PWM_ARR - 5u));
  HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_4);
}

void Pfc_PWMInit(void)
{
    
    /* TIM8 Counter Clock stopped when the core is halted */
    LL_DBGMCU_APB2_GRP1_FreezePeriph(LL_DBGMCU_APB2_GRP1_TIM8_STOP);

    uint32_t Brk2Timeout = 1000;

    /* disable main TIM counter to ensure*/
    LL_TIM_DisableCounter(TIM8);

    /* Enables the TIMx Preload on CC1 Register */
    LL_TIM_OC_EnablePreload(TIM8, LL_TIM_CHANNEL_CH1);
    /* Enables the TIMx Preload on CC2 Register */
    LL_TIM_OC_EnablePreload(TIM8, LL_TIM_CHANNEL_CH2);
    /* Enables the TIMx Preload on CC3 Register */
    LL_TIM_OC_EnablePreload(TIM8, LL_TIM_CHANNEL_CH3);
    /* Enables the TIMx Preload on CC4 Register */
    LL_TIM_OC_EnablePreload(TIM8, LL_TIM_CHANNEL_CH4);
    /* Prepare timer for synchronization */
    LL_TIM_GenerateEvent_UPDATE(TIM8);

    while ((LL_TIM_IsActiveFlag_BRK2(TIM8) == 1u) && (Brk2Timeout != 0u))
    {
        LL_TIM_ClearFlag_BRK2(TIM8);
        Brk2Timeout--;
    }

}


void PfcEnPwm(void)
{
    HAL_TIM_Base_Start(&htim8);
    LL_TIM_EnableIT_BRK(TIM8);
    /** @note 
    Bit 15 MOE: Main output enable
    This bit is cleared asynchronously by hardware as soon as one of the break inputs is active 
    (tim_brk or tim_brk2). It is set by software or automatically depending on the AOE bit. It is 
    acting only on the channels which are configured in output. 
    0:In response to a break 2 event. OC and OCN outputs are disabled
    In response to a break event or if MOE is written to 0: OC and OCN outputs are disabled or 
    forced to idle state depending on the OSSI bit.
    1:OC and OCN outputs are enabled if their respective enable bits are set (CCxE, CCxNE in 
    TIMx_CCER register).
    See OC/OCN enable description for more details (Section 28.6.11: TIMx capture/compare 
    enable register (TIMx_CCER)(x = 1, 8, 20)).
    */
    __HAL_TIM_MOE_ENABLE(&htim8);
    /* Enable PWM channel */
    LL_TIM_CC_EnableChannel(TIM8, TIMxCCER_MASK_CH123);
}

void PfcDisPwm(void)
{
    LL_TIM_DisableIT_BRK(TIM8);
    HAL_TIM_Base_Stop(&htim8);
    /* It forces inactive level on TIMx CHy and CHyN */
    LL_TIM_CC_DisableChannel(TIM8, TIMxCCER_MASK_CH123);

    LL_TIM_OC_SetCompareCH1(TIM8, TIM8->ARR >> 1);
    LL_TIM_OC_SetCompareCH2(TIM8, TIM8->ARR >> 1);
    LL_TIM_OC_SetCompareCH3(TIM8, TIM8->ARR >> 1);
    
}

uint32_t pwm_cnt = 0;
extern uint16_t pfc_adc_value[8];
uint32_t dac1_chnn1_reg = 0;
uint32_t dac1_chnn2_reg = 0;
int COMP  = 0;
static int CompR = 0;
static int CompS = 0;
static int CompT = 0;
CCMRAM void pfc_pwmctrl(void)
{
    static uint32_t tick;
    tick++;
    PFCVAR.SVPWM.U1 =    PFCVAR.Vout.beta;
    PFCVAR.SVPWM.U2 = (1.732050807568877f * PFCVAR.Vout.alpha - PFCVAR.Vout.beta) * 0.5f;
    PFCVAR.SVPWM.U3 = (-1.732050807568877f * PFCVAR.Vout.alpha - PFCVAR.Vout.beta) * 0.5f;
    PFCVAR.SVPWM.Sector = PFCVAR.SVPWM.SN
                          [
                              (PFCVAR.SVPWM.U1 > 0)
                              + 2 * (PFCVAR.SVPWM.U2 > 0)
                              + 4 * (PFCVAR.SVPWM.U3 > 0)
                          ];

    PFCVAR.SVPWM.K = 1.732050807568877f * DPLL3P.SysPar.Tpwm * 2.0f / DPLL3P.Vbus.In;

    PFCVAR.SVPWM.txyz[0] = PFCVAR.SVPWM.U1 * PFCVAR.SVPWM.K;
    PFCVAR.SVPWM.txyz[1] = PFCVAR.SVPWM.U2 * PFCVAR.SVPWM.K;
    PFCVAR.SVPWM.txyz[2] = PFCVAR.SVPWM.U3 * PFCVAR.SVPWM.K;
    PFCVAR.SVPWM.txyz[3] = -PFCVAR.SVPWM.txyz[0];
    PFCVAR.SVPWM.txyz[4] = -PFCVAR.SVPWM.txyz[1];
    PFCVAR.SVPWM.txyz[5] = -PFCVAR.SVPWM.txyz[2];

    PFCVAR.SVPWM.t1 = PFCVAR.SVPWM.txyz[PFCVAR.SVPWM.t12[0][PFCVAR.SVPWM.Sector]];
    PFCVAR.SVPWM.t2 = PFCVAR.SVPWM.txyz[PFCVAR.SVPWM.t12[1][PFCVAR.SVPWM.Sector]];

    float t1 = PFCVAR.SVPWM.t1;
    float t2 = PFCVAR.SVPWM.t2;
    if ((t1 + t2) > (DPLL3P.SysPar.Tpwm * 2.0f))
    {
        PFCVAR.SVPWM.t1 = t1 * (DPLL3P.SysPar.Tpwm * 2.0f) / (t1 + t2);
        PFCVAR.SVPWM.t2 = t2 * (DPLL3P.SysPar.Tpwm * 2.0f) / (t1 + t2);
    }

    PFCVAR.SVPWM.ton[0] = ((DPLL3P.SysPar.Tpwm * 2.0f) - PFCVAR.SVPWM.t1 - PFCVAR.SVPWM.t2) * 0.25f;
    if (PFCVAR.SVPWM.ton[0] < 0)
        PFCVAR.SVPWM.ton[0] = 0;
    PFCVAR.SVPWM.ton[1] = PFCVAR.SVPWM.ton[0] + PFCVAR.SVPWM.t1 * 0.5f;
    PFCVAR.SVPWM.ton[2] = PFCVAR.SVPWM.ton[1] + PFCVAR.SVPWM.t2 * 0.5f;

    PFCVAR.SVPWM.CntPhA = PFCVAR.SVPWM.ton[PFCVAR.SVPWM.CMP[0][PFCVAR.SVPWM.Sector]] ; 
    PFCVAR.SVPWM.CntPhB = PFCVAR.SVPWM.ton[PFCVAR.SVPWM.CMP[1][PFCVAR.SVPWM.Sector]] ; 
    PFCVAR.SVPWM.CntPhC = PFCVAR.SVPWM.ton[PFCVAR.SVPWM.CMP[2][PFCVAR.SVPWM.Sector]] ; 
    
    if (pfc.PFC_Runing == 1)
    {
        #if 0 //for test pwm 
        PFCVAR.SVPWM.CntPhA = 0;
        PFCVAR.SVPWM.CntPhB = 0;
        PFCVAR.SVPWM.CntPhC = 0 ;
        #endif
        /** @note clear first pha */
        if(pfc.PFC_PreRuning == 0)
        {
            pwm_cnt = 0;
        }
        
        pwm_cnt++;
        
        #ifdef DEBUG_PFC_USE_LOG
        if(pfc.PFC_PreRuning == 0)
        {
            vfd.debug_maxpwm[0] = 0;
            vfd.debug_maxpwm[1] = 0;
            vfd.debug_maxpwm[2] = 0;
            vfd.debug_pwmcnt = 0;
            vfd.debug_picnt  = 0;
            
        }
        
        vfd.debug_maxpwm[0] = (vfd.debug_maxpwm[0] > PFCVAR.SVPWM.CntPhA) ? vfd.debug_maxpwm[0] : PFCVAR.SVPWM.CntPhA;
        vfd.debug_maxpwm[1] = (vfd.debug_maxpwm[1] > PFCVAR.SVPWM.CntPhB) ? vfd.debug_maxpwm[1] : PFCVAR.SVPWM.CntPhB;
        vfd.debug_maxpwm[2] = (vfd.debug_maxpwm[2] > PFCVAR.SVPWM.CntPhC) ? vfd.debug_maxpwm[2] : PFCVAR.SVPWM.CntPhC;
        vfd.debug_pwmcnt++;  
        
        #endif
    }
        
    {
        LL_TIM_OC_SetCompareCH1(TIM8, PFCVAR.SVPWM.CntPhA);
        LL_TIM_OC_SetCompareCH2(TIM8, PFCVAR.SVPWM.CntPhB);
        LL_TIM_OC_SetCompareCH3(TIM8, PFCVAR.SVPWM.CntPhC); 
    }
    
    extern DAC_HandleTypeDef hdac1;

    uint16_t temp ;

    temp = DPLL3P.Iac.In.a * 0.1428f * 4096 ;
    
    HAL_DAC_SetValue(&hdac1,DAC_CHANNEL_1,DAC_ALIGN_12B_R,temp);
    
    temp = DPLL3P.Iac.In.a * 0.1428f * 4096 ;
    HAL_DAC_SetValue(&hdac1,DAC_CHANNEL_2,DAC_ALIGN_12B_R,temp);

}



////////////////////////////////////////////////////////////////////
//////////////Finsh Msh CMD
////////////////////////////////////////////////////////////////////
#ifdef RT_USING_FINSH
#include "finsh.h"


#endif