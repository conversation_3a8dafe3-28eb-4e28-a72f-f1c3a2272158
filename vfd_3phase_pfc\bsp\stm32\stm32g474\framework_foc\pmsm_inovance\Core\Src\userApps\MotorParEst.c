#include "MotorParaIDinclude.h"
#include "f_runSrc.h"

UV_BIAS_COFF_STRUCT      gUVCoff;
MOTOR_PARA_EST           gGetParVarable;
MOTOR_EXTERN_STRUCT      gMotorExtReg;
Uint  gTuneFinishFlag=0;
Uint gFlagEnd;

PAR_EST_MAIN_STEP const pmSVC_TuneProgLoad[IDENTIFY_PROGRESS_LENGTH] = {
    IDENTIFY_RS,      // im and pm 定子电阻辨识
    PM_EST_POLSE_POS, // pm 磁极位置辨识
    IDENTIFY_END,
    IDENTIFY_END,
    IDENTIFY_END
};
//---------------
// PAR_EST_MAIN_STEP const pmSVC_TuneProgNoLoad[IDENTIFY_PROGRESS_LENGTH] = {
//     IDENTIFY_RS,      // im and pm 定子电阻辨识
//     PM_EST_POLSE_POS, // pm 磁极位置辨识
//     PM_EST_BEMF,      // pm 反电动势辨识
//     IDENTIFY_END,
//     IDENTIFY_END
// };

PAR_EST_MAIN_STEP const pmSVC_TuneProgNoLoad[IDENTIFY_PROGRESS_LENGTH] = {
    IDENTIFY_RS,      // im and pm 定子电阻辨识
    PM_EST_POLSE_POS, // pm 磁极位置辨识
   PM_EST_BEMF,      // pm 反电动势辨识
 //  IDENTIFY_END,
   // IDENTIFY_END,
    IDENTIFY_END,
    IDENTIFY_END
};
//-------------------------------
PAR_EST_MAIN_STEP const noTuneProgress[IDENTIFY_PROGRESS_LENGTH] = {
    IDENTIFY_END,
    IDENTIFY_END,
    IDENTIFY_END,
    IDENTIFY_END,
    IDENTIFY_END
};

void Disable_TIM_CCxNChannelCmd(TIM_TypeDef *TIMx, uint32_t Channel, uint32_t ChannelNState)
{
  uint32_t tmp;

  tmp = TIM_CCER_CC1NE << (Channel & 0x1FU); /* 0x1FU = 31 bits max shift */

  /* Reset the CCxNE Bit */
  TIMx->CCER &=  ~tmp;

  /* Set or reset the CCxNE Bit */
  TIMx->CCER |= (uint32_t)(ChannelNState << (Channel & 0x1FU)); /* 0x1FU = 31 bits max shift */
}


void EndOfParIdentify(void)  
{
	if(gGetParVarable.QtEstDelay < 2)
	{
		gMainStatus.PrgStatus.all = 0;
        if(TUNE_FINISH != gGetParVarable.StatusWord)
        {
            gGetParVarable.StatusWord = TUNE_SUCCESS;

			funcCode.code.motorParaM1.elem.pmsmRs = gMotorExtReg.RsPm;
			funcCode.code.motorParaM1.elem.pmsmLd = gMotorExtReg.LD;
			funcCode.code.motorParaM1.elem.pmsmLq = gMotorExtReg.LQ;
			funcCode.code.motorParaM1.elem.pmsmCoeff = gMotorExtReg.BemfVolt;
///////////////////////////////

            // gPmParEst.IdKp = gVCPar.AcrImKp;
            // gPmParEst.IdKi = gVCPar.AcrImKi;
            // gPmParEst.IqKp = gVCPar.AcrItKp;
            // gPmParEst.IqKi = gVCPar.AcrItKi;
            // gVCPar.AcrImKp = funcCode.code.vcParaM1.mAcrKp;
            // gVCPar.AcrImKi = funcCode.code.vcParaM1.mAcrKi;
            // gVCPar.AcrItKp = funcCode.code.vcParaM1.tAcrKp;
            // gVCPar.AcrItKi = funcCode.code.vcParaM1.tAcrKi;
            funcCode.code.vcParaM1.mAcrKp=gPmParEst.IdKp;
            funcCode.code.vcParaM1.mAcrKi=gPmParEst.IdKi;
            funcCode.code.vcParaM1.tAcrKp=gPmParEst.IqKp;
            funcCode.code.vcParaM1.tAcrKi=gPmParEst.IqKi;
        }

        DINT;
    	DisableDrive();
    	__HAL_TIM_DISABLE_IT(&htim1, TIM_IT_UPDATE);
        __HAL_TIM_DISABLE_IT(&htim1, TIM_IT_CC4);
        __HAL_TIM_DISABLE(&htim1);
        __HAL_TIM_SET_COUNTER(&htim1, 1);
        ResetADCEndIsr();
		SetPwmMode(0x6060UL, 0x6060UL);
        SetPwmNPolarity(0,0,0);
        Disable_TIM_CCxNChannelCmd((&htim1)->Instance, TIM_CHANNEL_1, TIM_CCxN_ENABLE);

    	(&htim1)->Instance->CR1 &= 0xFF8F;
    	(&htim1)->Instance->CR1 |= 0x00E0;
    	(&htim1)->Instance->PSC = 0;
		__HAL_TIM_SET_AUTORELOAD(&htim1, gPeriod_BeforeEst);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, gPeriod_BeforeEst/2);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, gPeriod_BeforeEst/2);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, gPeriod_BeforeEst/2);


        HAL_ADCEx_MultiModeStop_DMA(&hadc1);
		HAL_NVIC_SetPriority(DMA2_Channel1_IRQn, 1, 0);
		HAL_NVIC_EnableIRQ(DMA2_Channel1_IRQn);
        User_ADC_Init();
        
        #if 0
        if( HAL_ADCEx_Calibration_Start(&hadc1,ADC_SINGLE_ENDED) != HAL_OK ) {
            Error_Handler();
        }
 
        HAL_ADC_Start_DMA(&hadc1, (uint32_t *)gAdcResult, ADC_12_CHNNS);

        #else
        
        if( HAL_ADCEx_Calibration_Start(&hadc1,ADC_SINGLE_ENDED) != HAL_OK ) {
            Error_Handler();
        }
        if( HAL_ADCEx_Calibration_Start(&hadc2,ADC_SINGLE_ENDED) != HAL_OK ) {
            Error_Handler();
        }
        
       
        if(HAL_ADCEx_MultiModeStart_DMA(&hadc1, gAdcResult, ADC_12_CHNNS) != HAL_OK){
        	Error_Handler();
        }
        HAL_ADCEx_MultiModeStart_DMA(&hadc1, gAdcResult, ADC_12_CHNNS); // �??动adc dma传输
        #endif
            //#: �?�??�中�??
		__HAL_TIM_CLEAR_FLAG(&htim1, TIM_FLAG_UPDATE); // 清除更新�??�??的标�??
		__HAL_TIM_CLEAR_FLAG(&htim1, TIM_FLAG_CC4); // 清除比较�??�??的标�??
		__HAL_TIM_ENABLE_IT(&htim1, TIM_IT_UPDATE); // 使能更新�??�??
		__HAL_TIM_ENABLE(&htim1); // 使能计数
		EINT;

	}
	else if( gGetParVarable.QtEstDelay >= 5 ) // 延时10ms
	{
		gMainStatus.RunStep = STATUS_STOP; // 进入停机状�?
        gTuneFinishFlag = 1; // 辨识结束标志
        dspSubStatus.bit.uiTuneStep = 3;

		gGetParVarable.IdSubStep = 1;
		gGetParVarable.ParEstMstep = 0;

		if(gFlagEnd)
		{
			gFlagEnd = 0;
		}
		else
		{
			gFlagEnd = 1;
		}
	}

	gGetParVarable.QtEstDelay ++; // 计数变量
    //wujian  ----waite for test function  gEstBemf.BemfVolt this value have some relationship with tune freq 
    if((gEstBemf.BemfVolt > gMotorInfo.Votage*12)||(gEstBemf.BemfVolt < gMotorInfo.Votage*4))
	{
	    gError.ErrorCode.all |= ERROR_COEFF_BIT;        //380V的话，在456�?152V之�?�就报�?�告
	}

}

void RunCaseGetPar(void)
{
    if((gError.ErrorCode.all != 0) || (gMainCmd.Command.bit.Start == 0))
    {
         if(gError.ErrorCode.all != 0)
        
        DisableDrive();
        gGetParVarable.ParEstContent[gGetParVarable.ParEstMstep] = IDENTIFY_END; // 结束�??�??
        gGetParVarable.StatusWord = TUNE_FINISH; // 参数�??学习结束
    }

    switch(gGetParVarable.ParEstContent[gGetParVarable.ParEstMstep])
    {
		case IDENTIFY_RS:
		{
			RsIdentify();
			break;
		}
    	case PM_EST_POLSE_POS:
    	{
    		SynTuneInitPos();
    		break;
    	}
    	case PM_EST_BEMF:
    	{
    		SynTuneBemf();
    		break;
    	}
    	default:
    	{
    		EndOfParIdentify();
    		break;
    	}
    }
}

void PrepareParForTune(void)
{
    int16 m_index;
    PAR_EST_MAIN_STEP *m_PIdentifyFlow;

    //@：所有参数辨识过程中返回的变量，都�?��?�先赋值，否则会�?�致对应的功能码出错
    gMotorExtReg.R1     = gMotorExtInfo.R1;
    gMotorExtReg.R2     = gMotorExtInfo.R2;
    gMotorExtReg.L0     = gMotorExtInfo.L0;
    gMotorExtReg.LM     = gMotorExtInfo.LM;
    gMotorExtReg.I0     = gMotorExtInfo.I0;

    gMotorExtReg.RsPm   = gMotorExtInfo.RsPm; // PM motor
    gMotorExtReg.LD     = gMotorExtInfo.LD;
    gMotorExtReg.LQ     = gMotorExtInfo.LQ;
    gEstBemf.BemfVolt   = gMotorExtInfo.BemfVolt; // PM �??子�?�链 %

    gPmParEst.IdKp = gVCPar.AcrImKp;
    gPmParEst.IdKi = gVCPar.AcrImKi;
    gPmParEst.IqKp = gVCPar.AcrItKp;
    gPmParEst.IqKi = gVCPar.AcrItKi;

    gGetParVarable.ParEstMstep = 0;
    gGetParVarable.StatusWord = TUNE_INITIAL;
    gGetParVarable.IdSubStep = 1; // 子过程�?��??
    gUVCoff.IdRsCnt = 0;
    gUVCoff.IdRsDelay = 0;

    gIPMZero.DetectCnt = 0; // must be initiated
    gGetParVarable.QtEstDelay = 0;

    switch(gGetParVarable.TuneType) // 决定了辨识的参数类型
    {
        case TUNE_PM_COMP_LOAD: { //#: 11 --- 同�?�机空载零点位置识别
            m_PIdentifyFlow = (PAR_EST_MAIN_STEP *)pmSVC_TuneProgLoad;

            break;
        }
        case TUNE_PM_COMP_NO_LOAD: { //#: 12 --- 同�?�机带载零点位置识别
            m_PIdentifyFlow = (PAR_EST_MAIN_STEP *) pmSVC_TuneProgNoLoad;
            
            break;
        }
        default: {
            m_PIdentifyFlow = (PAR_EST_MAIN_STEP *)noTuneProgress;
            break;
        }
    }

    for(m_index=0; m_index < IDENTIFY_PROGRESS_LENGTH; m_index++)
    {
        gGetParVarable.ParEstContent[m_index] = *(m_PIdentifyFlow + m_index);
    }

}

