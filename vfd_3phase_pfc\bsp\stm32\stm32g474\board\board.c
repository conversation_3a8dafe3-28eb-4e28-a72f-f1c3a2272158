/*
 * Copyright (c) 2006-2021, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2018-11-06     SummerGift   first version
 * 2019-10-03     xuz<PERSON>oyi     add stm32g431-st-nucleo bsp
 */

#include "board.h"

#define DBG_TAG    "board"
#define DBG_LVL    DBG_LOG
#include <rtdbg.h>

void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  HAL_PWREx_ControlVoltageScaling(PWR_REGULATOR_VOLTAGE_SCALE1_BOOST);
  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI|RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = RCC_PLLM_DIV2;
  RCC_OscInitStruct.PLL.PLLN = 85;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = RCC_PLLQ_DIV2;
  RCC_OscInitStruct.PLL.PLLR = RCC_PLLR_DIV2;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }
  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK)
  {
    Error_Handler();
  }
  /** Initializes the peripherals clocks
  */
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART1|RCC_PERIPHCLK_USART2
                              |RCC_PERIPHCLK_UART5|RCC_PERIPHCLK_ADC12
                              |RCC_PERIPHCLK_ADC345|RCC_PERIPHCLK_FDCAN;
  PeriphClkInit.Usart1ClockSelection = RCC_USART1CLKSOURCE_PCLK2;
  PeriphClkInit.Usart2ClockSelection = RCC_USART2CLKSOURCE_PCLK1;
  PeriphClkInit.Uart5ClockSelection = RCC_UART5CLKSOURCE_PCLK1;
  PeriphClkInit.FdcanClockSelection = RCC_FDCANCLKSOURCE_PCLK1;
  PeriphClkInit.Adc12ClockSelection = RCC_ADC12CLKSOURCE_SYSCLK;
  PeriphClkInit.Adc345ClockSelection = RCC_ADC345CLKSOURCE_SYSCLK;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

uint8_t rcc_reset_code = 0;
int BSP_RccFlagCheck(void)
{
    rt_kprintf("\r\n[BOARD] Power on bootloading\n.\n..\n...\n");
    
    if (__HAL_RCC_GET_FLAG(RCC_FLAG_IWDGRST) != 0x00u)
    {
        rt_kprintf("[BOARD] RCC_FLAG_IWDG \r\n");
        rcc_reset_code = 1;
    }
    else if (__HAL_RCC_GET_FLAG(RCC_FLAG_SFTRST) != 0x00u)
    {
        rt_kprintf("[BOARD] RCC_FLAG_SOFT \r\n");
        rcc_reset_code = 2;
    }
    else if (__HAL_RCC_GET_FLAG(RCC_FLAG_BORRST) != 0x00u)
    {
        rt_kprintf("[BOARD] RCC_FLAG_BOR \r\n");
        rcc_reset_code = 3;
    }
    else if (__HAL_RCC_GET_FLAG(RCC_FLAG_LPWRRST) != 0x00u)
    {
        rt_kprintf("[BOARD] RCC_FLAG_LPWRRST \r\n");
        rcc_reset_code = 4;
    }
    else if (__HAL_RCC_GET_FLAG(RCC_FLAG_PINRST) != 0x00u)
    {
        rt_kprintf("[BOARD] RCC_FLAG_PINRST \r\n");
        rcc_reset_code = 5;
    }
    
    /* Clear reset flags in any cases */
    __HAL_RCC_CLEAR_RESET_FLAGS();
    return 0;
}
INIT_COMPONENT_EXPORT(BSP_RccFlagCheck);

#ifdef RT_USING_FINSH
#include "finsh.h"
#include "string.h"

uint32_t _kernel_v = 0;
int cmd_soft_v(void)
{
    uint32_t app_offset = 0;
    
    
#if USE_BOOT
    app_offset = 0x1C000;
#else
    app_offset = 0x00000;
#endif
    
    _kernel_v    = *(uint32_t *)(FLASH_BASE + app_offset + 0x1C);
    vfd.version  = *(uint32_t *)(FLASH_BASE + app_offset + 0x1C);
    vfd.board_id = *(uint32_t *)(FLASH_BASE + app_offset + 0x20);
    vfd.product_type = vfd.board_id; 
    #if defined(VFD_TEST_DEBUG)
    _kernel_v = ((_kernel_v & 0xFFFF) % 10000) + 20000;   
    #elif defined(FOUT_QUADRANT)
    _kernel_v = ((_kernel_v & 0xFFFF) % 10000) + 25000;      
    #else
    _kernel_v = _kernel_v;
    #endif
    
}
INIT_BOARD_EXPORT(cmd_soft_v);

MSH_CMD_EXPORT_ALIAS(cmd_soft_v, soft_v, root version);

#endif