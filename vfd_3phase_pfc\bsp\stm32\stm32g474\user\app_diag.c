/******************** (C) COPYRIGHT 2021     ***********************************
* File Name          : app_diag.c
* Author             : xiou
* Version            : V1.0
* Date               :
* Description        :
********************************************************************************/
#include <rtthread.h>
#include <rtdevice.h>
#include "stm32g4xx.h"

#include "uapp.h"
#include "app_diag.h"

static uint32_t diagon_tick = 0;


#define TICK_PER_SECOND     100
#define TICK_PER_MIN        (TICK_PER_SECOND * 60)
#define DIAG_RESUME_CNT     (200) /*! 5s */

#define DN2TCMS  0x1
#define DN2REC   0x2
#define DN2LED   0x4                     
    
#define SOFT   0x1
#define HARD   0x2    
////////////////////////////////////////////////////////////////////////////////
//------------------------------------------------------------------------------
// Name:        sys_diag
//------------------------------------------------------------------------------
// Description: This function can be used to create a time delay.
//------------------------------------------------------------------------------
// code:        diag code to save or show
//   
//------------------------------------------------------------------------------
// level:      0:none 1:lock&stop 2:stop 3:only record
//  mask:      bit0: dnot2tcms
//             bit1: dnot2record
//             bit2: dnot2led 
//  type:      bit0: software  
//             bit1: hardware
// resume tick: 1=10ms,  tick last to clear  diag code
// happen tick: 1=10ms,  tick last to active diag code       
// str name :  console print diag name info                    
//------------------------------------------------------------------------------
////////////////////////////////////////////////////////////////////////////////  
const diag_t diag_table[] =
{
/*  code   level    mask  tyep             happen              resume      str name  */
/*                                         1=10ms              1=10ms                */
    {1,     LEV_3,     0, SOFT,                 0,                500,    "DiagLgc_ACIinLimit"},
    {2,     LEV_3,     0, SOFT,                 0,                500,    "DiagLgc_ACIoutLimit"},
    {3,     LEV_1,     0, HARD,               200,                500,    "DiagLgc_InverterIPM"},
    {4,     LEV_1,     0, HARD,               200,                500,    "DiagLgc_PfcIPM"},
    {5,     LEV_2,     0, SOFT,               100,                500,    "DiagLgc_ACVin_Over"},      // ≥463V持续1S，滞回453V
    {6,     LEV_2,     0, SOFT,               200,                500,    "DiagLgc_ACVin_Low"},       // ≤325V持续2S，滞回335V
    {7,     LEV_2,     0, SOFT,                10,                500,    "DiagLgc_Vbus_Over1"},      // ≥720V持续100ms，滞回715V
    {8,     LEV_2,     0, SOFT,                10,                500,    "DiagLgc_Vbus_Over2"},      // ≥750V持续100ms，滞回745V
    {9,     LEV_2,     0, SOFT,               500,                500,    "DiagLgc_Vbus_Low"},        // ≤250V持续5s，滞回255V
    {10,    LEV_2,     0, SOFT,               100,                500,    "DiagLgc_DCVin_Over"},
    {11,    LEV_2,     0, SOFT,               200,                500,    "DiagLgc_DCVin_Low"},
    {12,    LEV_2,     0, SOFT,               300,                500,    "DiagLgc_ACVin_LosePhaseR"},
    {13,    LEV_2,     0, SOFT,               300,                500,    "DiagLgc_ACVin_LosePhaseS"},
    {14,    LEV_2,     0, SOFT,               300,                500,    "DiagLgc_ACVin_LosePhaseT"},
    {15,    LEV_1,     0, SOFT,               300,                500,    "DiagLgc_ACIout_LosePhaseU"},
    {16,    LEV_1,     0, SOFT,               300,                500,    "DiagLgc_ACIout_LosePhaseV"},
    {17,    LEV_1,     0, SOFT,               300,                500,    "DiagLgc_ACIout_LosePhaseW"},
    {18,    LEV_1,     0, SOFT,                20,               1000,    "DiagLgc_AC_KMON1"},
    {19,    LEV_1,     0, SOFT,                50,                500,    "DiagLgc_PrechargeResist"},
    {20,    LEV_2,     0, SOFT,               300,                500,    "DiagLgc_ACVin_PhaseSeqc"},
    {21,    LEV_2,     0, SOFT,               300,                500,    "DiagLgc_ACVin_FreqLow"},   // <47Hz持续3S，47.5Hz恢复
    {22,    LEV_2,     0, SOFT,               300,                500,    "DiagLgc_ACVin_FreqOver"},  // >63Hz持续3S，62.5Hz恢复
    {23,    LEV_3,     0, SOFT,               500,                200,    "DiagLgc_SpiFlashMountErr"},
    {24,    LEV_2,     0, SOFT,                 5,                500,    "DiagLgc_AD_Ref"},
    {25,    LEV_2,     0, SOFT,               100,                500,    "DiagLgc_12V_Over"},        // >18V持续1S，滞回17V
    {26,    LEV_2,     0, SOFT,               100,                500,    "DiagLgc_12V_Low"},         // <12V持续1S，滞回13V
    {27,    LEV_2,     0, SOFT,               100,                500,    "DiagLgc_5V_Over"},         // >5.5V持续1S，滞回5.3V
    {28,    LEV_2,     0, SOFT,               100,                500,    "DiagLgc_5V_Low"},          // <4.5V持续1S，滞回4.7V
    {29,    LEV_2,     0, SOFT,               500,                500,    "DiagLgc_MainPowerMisWire"},
    {30,    LEV_1,     0, SOFT,                10,               1000,    "DiagLgc_ACIout_GroudU"},
    {31,    LEV_1,     0, SOFT,                10,               1000,    "DiagLgc_ACIout_GroudV"},
    {32,    LEV_1,     0, SOFT,                10,               1000,    "DiagLgc_ACIout_GroudW"},
    {33,    LEV_3,     0, SOFT,                 0,                200,    "DiagLgc_Com485Timeout"},
    {34,    LEV_3,     0, SOFT,                 0,                200,    "DiagLgc_ComCanTimeout"},
    {35,    LEV_3,     0, SOFT,               100,                200,    "DiagLgc_TempLimit"},
    {36,    LEV_2,     0, SOFT,                 0,                500,    "DiagLgc_Lock"},
    {37,    LEV_1,     0, HARD,               200,                500,    "DiagLgc_OVP_P_BUS"},
    {38,    LEV_1,     0, HARD,               200,                500,    "DiagLgc_ICP"},
    {39,    LEV_1,     0, HARD,               200,                500,    "DiagLgc_OCP"},
    {40,    LEV_1,     0, HARD,               300,                500,    "DiagLgc_F_HD"},
    {41,    LEV_2,     0, SOFT,               100,                500,    "DiagLgc_POW"},
    {42,    LEV_1,     0, SOFT,             60000,               1000,    "DiagLgc_ACIout_Overload110"},
    {43,    LEV_1,     0, SOFT,             45000,               1000,    "DiagLgc_ACIout_Overload120"},
    {44,    LEV_1,     0, SOFT,             30000,               1000,    "DiagLgc_ACIout_Overload130"},
    {45,    LEV_1,     0, SOFT,             15000,               1000,    "DiagLgc_ACIout_Overload140"},
    {46,    LEV_1,     0, SOFT,              9000,               1000,    "DiagLgc_ACIout_Overload150"},
    {47,    LEV_1,     0, SOFT,              4500,               1000,    "DiagLgc_ACIout_Overload160"},
    {48,    LEV_1,     0, SOFT,              1500,               1000,    "DiagLgc_ACIout_Overload170"},
    {49,    LEV_1,     0, SOFT,               500,               1000,    "DiagLgc_ACIout_Overload180"},
    {50,    LEV_1,     0, SOFT,               100,               1000,    "DiagLgc_ACIout_Overload190"},
    {51,    LEV_1,     0, SOFT,             60000,               1000,    "DiagLgc_ACIin_Overload110"},
    {52,    LEV_1,     0, SOFT,             45000,               1000,    "DiagLgc_ACIin_Overload120"},
    {53,    LEV_1,     0, SOFT,             30000,               1000,    "DiagLgc_ACIin_Overload130"},
    {54,    LEV_1,     0, SOFT,             15000,               1000,    "DiagLgc_ACIin_Overload140"},
    {55,    LEV_1,     0, SOFT,              9000,               1000,    "DiagLgc_ACIin_Overload150"},
    {56,    LEV_1,     0, SOFT,              4500,               1000,    "DiagLgc_ACIin_Overload160"},
    {57,    LEV_1,     0, SOFT,              1500,               1000,    "DiagLgc_ACIin_Overload170"},
    {58,    LEV_1,     0, SOFT,               500,               1000,    "DiagLgc_ACIin_Overload180"},
    {59,    LEV_1,     0, SOFT,               100,               1000,    "DiagLgc_ACIin_Overload190"},
    {60,    LEV_3,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Warn_1"},
    {61,    LEV_3,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Warn_2"},
    {62,    LEV_3,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Warn_3"},
    {63,    LEV_3,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Warn_4"},
    {64,    LEV_3,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Warn_5"},
    {65,    LEV_3,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Warn_6"},
    {66,    LEV_3,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Warn_7"},
    {67,    LEV_3,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Warn_8"},
    {68,    LEV_3,     0, SOFT,               200,                500,    "DiagLgc_OverMosi"},
    {69,    LEV_2,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Stop_1"},
    {70,    LEV_2,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Stop_2"},
    {71,    LEV_2,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Stop_3"},
    {72,    LEV_2,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Stop_4"},
    {73,    LEV_2,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Stop_5"},
    {74,    LEV_2,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Stop_6"},
    {75,    LEV_2,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Stop_7"},
    {76,    LEV_2,     0, SOFT,               200,                500,    "DiagLgc_OverTempt_Stop_8"},
    {77,    LEV_1,     0, SOFT,                 0,                500,    "DiagLgc_bus_short_circuit"},
    {78,    LEV_3,     0, SOFT,                 0,                200,    "DiagLgc_DCVin_Low_Warning"},
    {79,    LEV_2,     0, SOFT,                 0,                200,    "DiagLgc_ACIN_Invalid"},
    {80,    LEV_2,     0, SOFT,                 0,                200,    "DiagLgc_DCIN_Invalid"},
    {81,    LEV_1,     0, SOFT,                50,                500,    "DiagLgc_VBUS_SensorError"},
    {82,    LEV_1,     0, SOFT,                50,                500,    "DiagLgc_VIN_R_SensorError"},
    {83,    LEV_1,     0, SOFT,                50,                500,    "DiagLgc_VIN_S_SensorError"},
    {84,    LEV_1,     0, SOFT,                50,                500,    "DiagLgc_VIN_T_SensorError"},
    {85,    LEV_1,     0, SOFT,                50,                500,    "DiagLgc_IIN_R_SensorError"},
    {86,    LEV_1,     0, SOFT,                50,                500,    "DiagLgc_IIN_S_SensorError"},
    {87,    LEV_1,     0, SOFT,                50,                500,    "DiagLgc_IIN_T_SensorError"},
    {88,    LEV_1,     0, SOFT,                50,                500,    "DiagLgc_IOUT_U_SensorError"},
    {89,    LEV_1,     0, SOFT,                50,                500,    "DiagLgc_IOUT_V_SensorError"},
    {90,    LEV_1,     0, SOFT,                50,                500,    "DiagLgc_IOUT_W_SensorError"},
    {91,    LEV_3,     0, SOFT,                50,                200,    "DiagLgc_TemptSensor_Error_1"},
    {92,    LEV_3,     0, SOFT,                50,                200,    "DiagLgc_TemptSensor_Error_2"},
    {93,    LEV_3,     0, SOFT,                50,                200,    "DiagLgc_TemptSensor_Error_3"},
    {94,    LEV_3,     0, SOFT,                50,                200,    "DiagLgc_TemptSensor_Error_4"},
    {95,    LEV_3,     0, SOFT,                50,                200,    "DiagLgc_TemptSensor_Error_5"},
    {96,    LEV_3,     0, SOFT,                50,                200,    "DiagLgc_TemptSensor_Error_6"},
    {97,    LEV_3,     0, SOFT,                50,                200,    "DiagLgc_TemptSensor_Error_7"},
    {98,    LEV_3,     0, SOFT,                50,                200,    "DiagLgc_TemptSensor_Error_8"},
    {99,    LEV_3,     0, SOFT,                50,                500,    "DiagLgc_VOUT_U_SensorError"},
    {100,   LEV_3,     0, SOFT,                50,                500,    "DiagLgc_VOUT_V_SensorError"},
    {101,   LEV_3,     0, SOFT,                50,                500,    "DiagLgc_VOUT_W_SensorError"},
};

/**
  * @brief 
  * @param 
  * @retval 
  */
uint8_t diag_table_len(void)
{
    return (sizeof(diag_table)/sizeof(diag_t));
}

/**
  * @brief 
  * @param 
  * @retval 
  */
diag_t *diag_table_ptr(void)
{
    return diag_table;
}



/**
  * @brief 
  * @param 
  * @retval 
  */
void diag_poweron_reset(void)
{
    hardware_irq_pin.fo1_flag = RT_FALSE;
    hardware_irq_pin.fo2_flag = RT_FALSE;
    hardware_irq_pin.fo3_flag = RT_FALSE;
    hardware_irq_pin.f_tim1_breakin_flag = RT_FALSE;
    hardware_irq_pin.f_tim8_breakin_flag = RT_FALSE;
    hardware_irq_pin.f_hrtim_breakin_flag = RT_FALSE;
}

/**
  * @brief 输入电流高于7.2A，进入输入超功率限额运行，限制逆变输出频率，以回差0.5A为参考值（6.7A）限制。
  * @param 
  * @retval 
  */
void DiagLgc_ACIinLimit(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.pid_acin_power.Out != 0);

    _IsNormal = !_IsFault && !vfd.ctrl.load_reducing;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 输出电流高于8.5A，进入输入超功率限额运行，限制逆变输出频率，以回差0.5A为参考值（8.0A）限制。
  * @param 
  * @retval 
  */
void DiagLgc_ACIoutLimit(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (vfd.pid_acout_power.Out != 0);

    _IsNormal = !_IsFault && !vfd.ctrl.load_reducing;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}



/**
  * @brief 交流输入过压故障检测 - 规范要求：≥463V持续1S，滞回453V恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACVin_Over(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_ENABLE_AC380 &&
                vfd.bit.ad_init  &&  
                ((vfd.filter_ad.ac_vin_r >= AC_VIN_OVER)    || 
                (vfd.filter_ad.ac_vin_s >= AC_VIN_OVER)     ||
                (vfd.filter_ad.ac_vin_t >= AC_VIN_OVER));

    _IsNormal = (vfd.filter_ad.ac_vin_r <= AC_VIN_OVER_RESUME)  && 
                (vfd.filter_ad.ac_vin_s <= AC_VIN_OVER_RESUME)  &&
                (vfd.filter_ad.ac_vin_t <= AC_VIN_OVER_RESUME) ;
    
    _IsNormal = _IsNormal || (!VFD_ENABLE_AC380);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
    

}

/**
  * @brief 交流输入欠压故障检测 - 规范要求：≤325V持续2S，滞回335V恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACVin_Low(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_ENABLE_AC380  &&
                vfd.bit.ad_init   && 
                (AC380_FreqIs_Ok) &&
                ((((vfd.filter_ad.ac_vin_r <= AC_VIN_LOW)  && (vfd.filter_ad.ac_vin_r >= 50))  && 
                ((vfd.filter_ad.ac_vin_s  <= AC_VIN_LOW)  && (vfd.filter_ad.ac_vin_s >= 50))  &&
                ((vfd.filter_ad.ac_vin_t  <= AC_VIN_LOW)  && (vfd.filter_ad.ac_vin_t >= 50)) )
                || vfd.bit.acvin_low_stop);

    _IsNormal =  !VFD_ENABLE_AC380 ||
                    ((vfd.filter_ad.ac_vin_r >= AC_VIN_LOW_RESUME)  && 
                    (vfd.filter_ad.ac_vin_s >= AC_VIN_LOW_RESUME)  &&
                    (vfd.filter_ad.ac_vin_t >= AC_VIN_LOW_RESUME)) ;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
    if(diag_bit || !VFD_AC380_VALID)
        vfd.bit.acvin_low_stop = 0;
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIN_Invalid(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_ENABLE_AC380  &&
                vfd.bit.ad_init   && 
                (((vfd.filter_ad.ac_vin_r  < 50))  && 
                ((vfd.filter_ad.ac_vin_s   < 50))  &&
                ((vfd.filter_ad.ac_vin_t   < 50)) );

    _IsNormal =  !VFD_ENABLE_AC380 ||
                    ((vfd.filter_ad.ac_vin_r >= 50) || 
                    (vfd.filter_ad.ac_vin_s >= 50)  ||
                    (vfd.filter_ad.ac_vin_t >= 50)) ;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);

}

/**
  * @brief DC输入过压检测
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_DCVin_Over(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);

}

/**
  * @brief DC输入欠压检测
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_DCVin_Low(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);

}

/**
  * @brief DC输入欠压警告检测
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_DCVin_Low_Warning(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;

    _IsNormal = !_IsFault;  // 44V恢复

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);

}

/**
  * @brief DC输入无效检测
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_DCIN_Invalid(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = 0;

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);

}

/**
  * @brief 母线一级过压故障检测 - 规范要求：≥720V持续100ms，滞回715V恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_Vbus_Over1(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    float vbus = 0;
    
    if(!vfd.bit.ad_init)
        return;
    
    vbus = (vfd.filter_ad.vbus_inv > vfd.filter_ad.vbus_inv) ? vfd.filter_ad.vbus_inv : vfd.filter_ad.vbus_inv; /* choose the bigger */
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  ((float)vbus >= VBUS_OVER1) 
                && ((float)vbus < VBUS_OVER2);

    _IsNormal = ((float)vbus < VBUS_RESUME);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 母线二级过压故障检测 - 规范要求：≥750V持续100ms，滞回745V恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_Vbus_Over2(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    float vbus = 0;
    
    if(!vfd.bit.ad_init)
        return;
    
    vbus = (vfd.filter_ad.vbus_inv > vfd.filter_ad.vbus_inv) ? vfd.filter_ad.vbus_inv : vfd.filter_ad.vbus_inv; /* choose the bigger */
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  ((float)vbus >= VBUS_OVER2);

    _IsNormal = ((float)vbus < VBUS_RESUME);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}


/**
  * @brief 母线欠压故障检测 - 规范要求：≤250V持续5s，滞回255V恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_Vbus_Low(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    float vbus = 0;
    
    if(!vfd.bit.ad_init)
        return;
    
    vbus = (vfd.filter_ad.vbus_inv > vfd.filter_ad.vbus_inv) ? vfd.filter_ad.vbus_inv : vfd.filter_ad.vbus_inv; /* choose the smaller */
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (PFC_IS_RUN || DCDC_IS_RUN) &&
                ((float)vbus >= 50) &&
                ((float)vbus <= VBUS_LOW);

    _IsNormal = !(PFC_IS_RUN || DCDC_IS_RUN)
                ||                
                ((float)vbus > VBUS_LOW_RESUME);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}



/**
  * @brief 输入交流过载保护1 - 规范要求：变频器额定电流97.67A，过载保护
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIin_Over1(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_ENABLE_AC380 &&
                (((float)vfd.filter_ad.ac_iin_r >= AC_IIN_OVER1) ||
                ((float)vfd.filter_ad.ac_iin_s  >= AC_IIN_OVER1)  ||
                ((float)vfd.filter_ad.ac_iin_t  >= AC_IIN_OVER1)) ;

    _IsNormal =  !VFD_ENABLE_AC380                        ||
                (((float)vfd.filter_ad.ac_iin_r <= AC_IIN_RESUME) &&
                ((float)vfd.filter_ad.ac_iin_s  <= AC_IIN_RESUME)  &&
                ((float)vfd.filter_ad.ac_iin_t  <= AC_IIN_RESUME)) ;
                

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 输入交流过载保护2 - 规范要求：变频器额定电流97.67A，严重过载保护
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIin_Over2(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_ENABLE_AC380 && 
            ((vfd.fast_ad.ac_iin_r > AC_IIN_OVER2) || (vfd.fast_ad.ac_iin_s > AC_IIN_OVER2) || (vfd.fast_ad.ac_iin_t > AC_IIN_OVER2));

    _IsNormal = !_IsFault;
                

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}




/**
  * @brief 输出交流过载保护1 - 规范要求：电机额定电流97.67A，过载保护
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIout_Overload1(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0 ;

    _IsNormal = !_IsFault;
                

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 输出交流过载保护2 - 规范要求：电机额定电流97.67A，严重过载保护
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIout_Over2(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    static uint32_t over_cnter = 0;
    uint32_t timeout_cnt = 60000;
    float power = 0;
    
    power = vfd.acout_capacity;
    
    if(power >= (IOUT_OVERLOAD_BASE*1.1f))
    {
        if(over_cnter <= timeout_cnt)
            over_cnter++;
    }
    else if(power <= (IOUT_OVERLOAD_BASE*1.0f))
    {
        if(over_cnter > 0)
            over_cnter--;
    }
    
    if(power >= (IOUT_OVERLOAD_BASE*1.9f))      timeout_cnt = 100;
    else if(power >= (IOUT_OVERLOAD_BASE*1.8f)) timeout_cnt = 500;
    else if(power >= (IOUT_OVERLOAD_BASE*1.7f)) timeout_cnt = 1500;
    else if(power >= (IOUT_OVERLOAD_BASE*1.6f)) timeout_cnt = 4500;
    else if(power >= (IOUT_OVERLOAD_BASE*1.5f)) timeout_cnt = 9000;
    else if(power >= (IOUT_OVERLOAD_BASE*1.4f)) timeout_cnt = 15000;
    else if(power >= (IOUT_OVERLOAD_BASE*1.3f)) timeout_cnt = 30000;
    else if(power >= (IOUT_OVERLOAD_BASE*1.2f)) timeout_cnt = 45000;
    else if(power >= (IOUT_OVERLOAD_BASE*1.1f)) timeout_cnt = 60000;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (over_cnter >= timeout_cnt);

    _IsNormal = !_IsFault;
                

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}


extern uint8_t overtemp_flag;
/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_TempLimit(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = (overtemp_flag != 0) && ((vfd.pid_acin_power.Out != 0) || (vfd.pid_acout_power.Out != 0) || (vfd.pid_dcin_power.Out != 0));
            
    _IsNormal = !_IsFault;
                
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_Lock(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  vfd.bit.lock_stop;
            
    _IsNormal = !_IsFault;
                
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

#define OVER_TEMPT_RESUME   85  /* 85 度 */

/**
  * @brief   交流模式：PFC模块温度或逆变模块温度两个温度同时超过100℃时，降载至1/2额定功率（1500W）
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_1(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;

     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 紧急通风模式：全桥升压模块温度或逆变模块温度或DCDC变压器温度三个温度超过100℃时，降载至1/2额定功率（650W）。
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_2(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;
    
         ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief PFC电感温度超过110℃时，降载至1/2额定功率（1500W）。
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_3(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 母线电容温度超过95℃时，降载至1/2额定功率（1500W）。
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_4(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_5(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_6(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_7(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_8(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;

     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_9(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.filter_ad.temp_pfc_mos >= 105) && !vfd.diag.dc_114; 
    _IsFault =  _IsFault &&  VFD_ENABLE_AC380;
    _IsNormal = (vfd.filter_ad.temp_pfc_mos <= OVER_TEMPT_RESUME);
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_10(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.filter_ad.temp_inv_mos >= 105) && !vfd.diag.dc_115;    
    _IsNormal = (vfd.filter_ad.temp_inv_mos <= OVER_TEMPT_RESUME);
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_11(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  (vfd.filter_ad.temp_hdc1080 >= 100) && !vfd.diag.dc_116;    
    _IsNormal = (vfd.filter_ad.temp_hdc1080 <= OVER_TEMPT_RESUME);
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Warn_12(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverMosi(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
     _IsFault =  !vfd.diag.dc_116 && (vfd.filter_ad.mosi_hdc1080 >= 90) && (vfd.filter_ad.temp_hdc1080 <= 95);
    _IsNormal =  (vfd.filter_ad.mosi_hdc1080 <= 85);
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}
/**
  * @brief   交流模式：PFC模块温度或逆变模块温度两个温度同时超过100℃时，降载至1/2额定功率（1500W）
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_1(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 紧急通风模式：全桥升压模块温度或逆变模块温度或DCDC变压器温度三个温度超过100℃时，降载至1/2额定功率（650W）。
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_2(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;
         ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief PFC电感温度超过110℃时，降载至1/2额定功率（1500W）。
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_3(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 母线电容温度超过95℃时，降载至1/2额定功率（1500W）。
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_4(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_5(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_6(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.filter_ad.temp_BUS_CAP >= 100)&& !vfd.diag.dc_111; 
    _IsNormal = (vfd.filter_ad.temp_BUS_CAP <= OVER_TEMPT_RESUME);
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_7(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_8(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.filter_ad.temp_PFC_L >= 110)&& !vfd.diag.dc_113; 
    _IsFault =  _IsFault &&  VFD_ENABLE_AC380;
    _IsNormal = (vfd.filter_ad.temp_PFC_L <= OVER_TEMPT_RESUME);
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_9(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.filter_ad.temp_pfc_mos >= 110)&& !vfd.diag.dc_114; 
    _IsFault =  _IsFault &&  VFD_ENABLE_AC380;
    _IsNormal = (vfd.filter_ad.temp_pfc_mos <= OVER_TEMPT_RESUME);
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_10(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.filter_ad.temp_inv_mos >= 110)&& !vfd.diag.dc_115; 
    _IsNormal = (vfd.filter_ad.temp_inv_mos <= OVER_TEMPT_RESUME);
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_11(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OverTempt_Stop_12(uint8_t *diag,int offset)
{
    static uint32_t faultCnt  = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}
/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACVin_LosePhaseR(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    uint16_t vbus = 0;
    
    if(!vfd.bit.ad_init)
        return;
     ///////////////////////////////////////////////////////////////////////////////////
    uint8_t before_precharge_losephase,after_precharge_losephase;
    
    after_precharge_losephase = (vfd.ctrl.kmon3||vfd.ctrl.kmon1) && (vfd.filter_ad.ac_vin_s > AC_VIN_VALID)
                && (vfd.filter_ad.ac_vin_r < (vfd.filter_ad.ac_vin_s*0.7))  && (vfd.filter_ad.ac_vin_r > (vfd.filter_ad.ac_vin_s*0.4))
                && (vfd.filter_ad.ac_vin_t < (vfd.filter_ad.ac_vin_s*0.7))  && (vfd.filter_ad.ac_vin_t > (vfd.filter_ad.ac_vin_s*0.4));
    
    before_precharge_losephase = (!vfd.ctrl.kmon3&&!vfd.ctrl.kmon1) &&(vfd.filter_ad.ac_vin_s > AC_VIN_VALID)
                && (vfd.filter_ad.ac_vin_r < (vfd.filter_ad.ac_vin_s*0.7))  && (vfd.filter_ad.ac_vin_r > (vfd.filter_ad.ac_vin_s*0.2))
                && (vfd.filter_ad.ac_vin_t < (vfd.filter_ad.ac_vin_s*0.8))  && (vfd.filter_ad.ac_vin_t > (vfd.filter_ad.ac_vin_s*0.5));
    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_SWITCH_AC380 && (after_precharge_losephase || before_precharge_losephase);           
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 220 270 399
  * @param  
  * @retval 
  */
void DiagLgc_ACVin_LosePhaseS(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    uint16_t vbus = 0;
    
    if(!vfd.bit.ad_init)   
        return;
    
    uint8_t before_precharge_losephase,after_precharge_losephase;
    
    after_precharge_losephase = (vfd.ctrl.kmon3||vfd.ctrl.kmon1) && (vfd.filter_ad.ac_vin_t > AC_VIN_VALID)
                && (vfd.filter_ad.ac_vin_r < (vfd.filter_ad.ac_vin_t*0.7))  && (vfd.filter_ad.ac_vin_r > (vfd.filter_ad.ac_vin_t*0.4))
                && (vfd.filter_ad.ac_vin_s < (vfd.filter_ad.ac_vin_t*0.7))  && (vfd.filter_ad.ac_vin_s > (vfd.filter_ad.ac_vin_t*0.4));
    
    before_precharge_losephase = (!vfd.ctrl.kmon3&&!vfd.ctrl.kmon1) &&(vfd.filter_ad.ac_vin_t > AC_VIN_VALID)
                && (vfd.filter_ad.ac_vin_r < (vfd.filter_ad.ac_vin_t*0.7))  && (vfd.filter_ad.ac_vin_r > (vfd.filter_ad.ac_vin_t*0.2))
                && (vfd.filter_ad.ac_vin_s < (vfd.filter_ad.ac_vin_t*0.8))  && (vfd.filter_ad.ac_vin_s > (vfd.filter_ad.ac_vin_t*0.5));
     
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_SWITCH_AC380 && (after_precharge_losephase || before_precharge_losephase);
    
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACVin_LosePhaseT(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    uint16_t vbus = 0;
    
    if(!vfd.bit.ad_init)
        return;
    
    uint8_t before_precharge_losephase,after_precharge_losephase;
    
    after_precharge_losephase = (vfd.ctrl.kmon3||vfd.ctrl.kmon1) && (vfd.filter_ad.ac_vin_r > AC_VIN_VALID)
                && (vfd.filter_ad.ac_vin_s < (vfd.filter_ad.ac_vin_r*0.7))  && (vfd.filter_ad.ac_vin_s > (vfd.filter_ad.ac_vin_r*0.4))
                && (vfd.filter_ad.ac_vin_t < (vfd.filter_ad.ac_vin_r*0.7))  && (vfd.filter_ad.ac_vin_t > (vfd.filter_ad.ac_vin_r*0.4));
    
    before_precharge_losephase = (!vfd.ctrl.kmon3&&!vfd.ctrl.kmon1) &&(vfd.filter_ad.ac_vin_r > AC_VIN_VALID)
                && (vfd.filter_ad.ac_vin_s < (vfd.filter_ad.ac_vin_r*0.7))  && (vfd.filter_ad.ac_vin_s > (vfd.filter_ad.ac_vin_r*0.2))
                && (vfd.filter_ad.ac_vin_t < (vfd.filter_ad.ac_vin_r*0.8))  && (vfd.filter_ad.ac_vin_t > (vfd.filter_ad.ac_vin_r*0.5));
     
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_SWITCH_AC380 && (after_precharge_losephase || before_precharge_losephase);
               
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_LosePhaseU(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  INVERTER_IS_RUN &&
					(vfd.filter_ad.ac_iout_u < 0.5f) &&
                    ((fabs(vfd.filter_ad.ac_iout_u - vfd.filter_ad.ac_iout_v) >= ACIOUT_LOSEPHASE_CURR)     &&
                     (fabs(vfd.filter_ad.ac_iout_u - vfd.filter_ad.ac_iout_w) >= ACIOUT_LOSEPHASE_CURR));
               
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_LosePhaseV(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  INVERTER_IS_RUN &&
					(vfd.filter_ad.ac_iout_v < 0.5f) &&
                    ((fabs(vfd.filter_ad.ac_iout_v - vfd.filter_ad.ac_iout_u) >= ACIOUT_LOSEPHASE_CURR)     &&
                     (fabs(vfd.filter_ad.ac_iout_v - vfd.filter_ad.ac_iout_w) >= ACIOUT_LOSEPHASE_CURR));
               
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_LosePhaseW(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  INVERTER_IS_RUN &&
					(vfd.filter_ad.ac_iout_w < 0.5f) &&
                    ((fabs(vfd.filter_ad.ac_iout_w - vfd.filter_ad.ac_iout_v) >= ACIOUT_LOSEPHASE_CURR)     &&
                     (fabs(vfd.filter_ad.ac_iout_w - vfd.filter_ad.ac_iout_u) >= ACIOUT_LOSEPHASE_CURR));
               
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}



/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_AC_KMON1(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = VFD_VIN_OK && VFD_ENABLE_AC380 
                && (((PFCVAR.PIVbus.Out > 0.8f) && (vfd.filter_ad.ac_iin_r <= 1.0f))
                    || (vfd.ctrl.kmon1 && (vfd.filter_ad.vbus_inv < (vfd.filter_ad.ac_vin_r*1.2f))));
               
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_PrechargeResist(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    float vbus = 0;
    
    vbus = (vfd.filter_ad.vbus_inv > vfd.filter_ad.vbus_inv) ? vfd.filter_ad.vbus_inv : vfd.filter_ad.vbus_inv; /* choose the smaller */
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;
               
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 5V电源过压故障检测 - 规范要求：>5.5V持续1S，滞回5.3V恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_5V_Over(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.filter_ad.dc_5V >= DC5V_OVER);
               
    _IsNormal = (vfd.filter_ad.dc_5V <= DC5V_OVER_RESUME);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 5V电源欠压故障检测 - 规范要求：<4.5V持续1S，滞回4.7V恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_5V_Low(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.filter_ad.dc_5V <= DC5V_LOW);
               
    _IsNormal = (vfd.filter_ad.dc_5V >= DC5V_LOW_RESUME);
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}


/**
  * @brief 15V电源过压故障检测 - 规范要求：>18V持续1S，滞回17V恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_12V_Over(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    if(!vfd.bit.ad_init)
        return;
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.filter_ad.dc_12V >= DC12V_OVER);
               
    _IsNormal = (vfd.filter_ad.dc_12V <= DC12V_OVER_RESUME);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 15V电源欠压故障检测 - 规范要求：<12V持续1S，滞回13V恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_12V_Low(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.filter_ad.dc_12V <= DC12V_LOW);
               
    _IsNormal = (vfd.filter_ad.dc_12V >= DC12V_LOW_RESUME);
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}


/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_SpiFlashMountErr(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  vfd.bit.spiflash_init && !vfd.bit.mount_fs;
               
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACVin_PhaseSeqc(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.acin_freq < -40.0f);
               
    _IsNormal = (vfd.acin_freq > -5.0f);
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}


/**
  * @brief 输入频率过低故障检测 - 规范要求：<47Hz持续3S，47.5Hz持续5s恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACVin_FreqLow(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_ENABLE_AC380                  && 
                (vfd.bit.freq_err_flag            ||
                (Fast_AC380V_Is_Ok && (vfd.abs_fast_acin_freq < ACIN_FREQ_LOW)   && (vfd.abs_fast_acin_freq > 5)));
               
    _IsNormal = !VFD_ENABLE_AC380 || (vfd.abs_fast_acin_freq >= ACIN_FREQ_LOW_RESUME);
            
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
    if(diag_bit || !VFD_ENABLE_AC380)
        vfd.bit.freq_err_flag = 0;
}

/**
  * @brief 输入频率过高故障检测 - 规范要求：>63Hz持续3S，62.5Hz持续5s恢复
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACVin_FreqOver(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_ENABLE_AC380 
                &&  Fast_AC380V_Is_Ok
                && (vfd.abs_fast_acin_freq >= ACIN_FREQ_OVER);
               
    _IsNormal =  !VFD_ENABLE_AC380 || (vfd.abs_fast_acin_freq <= ACIN_FREQ_OVER_RESUME);
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}


/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_PfcIPM(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (!DIO_READ_BIT(F_IPM_PFC_PIN)          || (DIO_IRQ_DELAY(F_IPM_PFC_PIN) > 0))  ||       
                    LL_TIM_IsActiveFlag_BRK(TIM8)      ||  LL_TIM_IsActiveFlag_BRK2(TIM8);
    
    if(!VFD_VIN_OK && _IsFault && DIO_READ_BIT(F_IPM_PFC_PIN))
    {
        DIO_IRQ_DELAY(F_IPM_PFC_PIN) = 0;
        hardware_irq_pin.f_tim8_breakin_flag = 0;
        LL_TIM_ClearFlag_BRK2(TIM8);
        LL_TIM_ClearFlag_BRK(TIM8);
    }
    
    _IsFault =  _IsFault && VFD_ENABLE_AC380  && VFD_VIN_OK && FHD_ERROR;
    _IsNormal = !_IsFault;
               
    _IsFault = 0;// disable
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_DCDCIPM(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = 0;         
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
    if(diag)
        hardware_irq_pin.f_hrtim_breakin_flag = 0;
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_InverterIPM(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (!DIO_READ_BIT(F_IPM_INV_PIN)    || (DIO_IRQ_DELAY(F_IPM_INV_PIN) > 0))    || 
                 LL_TIM_IsActiveFlag_BRK(TIM1)   ||  LL_TIM_IsActiveFlag_BRK2(TIM1);
    
    if(!VFD_VIN_OK && _IsFault && DIO_READ_BIT(F_IPM_INV_PIN))
    {
        DIO_IRQ_DELAY(F_IPM_INV_PIN) = 0;
        hardware_irq_pin.f_tim1_breakin_flag = 0;
        LL_TIM_ClearFlag_BRK2(TIM1);
        LL_TIM_ClearFlag_BRK(TIM1);
    }
    
    _IsFault = _IsFault && VFD_VIN_OK && FHD_ERROR;
    _IsNormal = !_IsFault;
               
    _IsFault = 0;// disable
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
    if(diag)
        hardware_irq_pin.f_tim1_breakin_flag = 0;
    
    
}


/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_AD_Ref(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    float vref_sum = 0;
     ///////////////////////////////////////////////////////////////////////////////////
    
    if(!vfd.bit.ad_init) return;
    vref_sum += acvin_r._avr_adv;
    vref_sum += acvin_s._avr_adv;
    vref_sum += acvin_t._avr_adv;
    vref_sum += aciin_r._avr_adv;
    vref_sum += aciin_s._avr_adv;
    vref_sum += aciin_t._avr_adv;

    _IsFault  = vfd.bit.ad_init && (vref_sum >= 16.7f) || (vref_sum <= 5.0f) && (!Fast_DC5V_Is_Ok) && (!Fast_DC15V_Is_Ok);
               
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 主电源接错故障
  *        能正常检测到母线电压≥300V，三相输入电压均≤50V，三相输出电压均≥50V，报“主电源接错”不允许启动逆变与吸合吸合母线主继电器；
  * @param 
  * @retval 
  */
void DiagLgc_MainPowerMisWire(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    static uint8_t  flag = 0;
    static float  prev_vbus = 0;
    static uint32_t  sec_tick = 0;
    static uint8_t  vbus_stable_flag = 0;
    
    
    if(rt_tick_get()/RT_TICK_PER_SECOND != sec_tick)
    {
        sec_tick = rt_tick_get()/RT_TICK_PER_SECOND;
        
        if((fabs(prev_vbus - vfd.filter_ad.vbus_inv) <= 5)
            && ((prev_vbus - vfd.filter_ad.vbus_inv) < 5))
        {
            vbus_stable_flag = 1;
        }    
        else
        {
            vbus_stable_flag = 0;
            prev_vbus = vfd.filter_ad.vbus_inv;
        }
    }
    
    _IsFault =   VFD_SWITCH_AC380  && 
                 vbus_stable_flag  &&
                 vfd.bit.ad_init && 
                (vfd.filter_ad.vbus_inv >= 300) &&
                (vfd.filter_ad.ac_vin_r <= 50)  &&
                (vfd.filter_ad.ac_vin_s <= 50)  &&
                (vfd.filter_ad.ac_vin_t <= 50)  &&
                (vfd.filter_ad.ac_vout_u >= 50) &&
                (vfd.filter_ad.ac_vout_v >= 50) &&
                (vfd.filter_ad.ac_vout_w >= 50) ;
               
    _IsNormal = (vfd.filter_ad.vbus_inv <= 300) &&
                (vfd.filter_ad.ac_vin_r >= 50)  &&
                (vfd.filter_ad.ac_vin_s >= 50)  &&
                (vfd.filter_ad.ac_vin_t >= 50);
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   

}


/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_GroudU(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    _IsFault =  vfd.bit.ad_init && INVERTER_IS_RUN 
                && (vfd.filter_ad.ac_iout_u >= 1.0f)      
                && (vfd.filter_ad.ac_iout_v <= (vfd.filter_ad.ac_iout_u/2))
                && (vfd.filter_ad.ac_iout_w <= (vfd.filter_ad.ac_iout_u/2))
                ;
               
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_GroudV(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    _IsFault =  vfd.bit.ad_init && INVERTER_IS_RUN   
                && (vfd.filter_ad.ac_iout_v >= 1.0f)      
                && (vfd.filter_ad.ac_iout_u <= (vfd.filter_ad.ac_iout_v/2))
                && (vfd.filter_ad.ac_iout_w <= (vfd.filter_ad.ac_iout_v/2))
                ;
               
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_GroudW(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    _IsFault =  vfd.bit.ad_init && INVERTER_IS_RUN
                && (vfd.filter_ad.ac_iout_w >= 1.0f)      
                && (vfd.filter_ad.ac_iout_u <= (vfd.filter_ad.ac_iout_w/2))
                && (vfd.filter_ad.ac_iout_v <= (vfd.filter_ad.ac_iout_w/2))
                ;
               
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_Com485Timeout(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    static uint8_t flag = 0;
    
    if(com_modbus.flag_normal)
        flag = 1;
    
    _IsFault =  flag && com_modbus.flag_timeout ;
               
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ComCanTimeout(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    static uint8_t flag = 0;
    
    if(com_can.flag_normal)
        flag = 1;
    
    _IsFault =  flag && com_can.flag_timeout ;
               
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_DCDC_OverTime(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    static uint32_t delayCnt = 0;

    if(nvs_datas.modbus.reg_3000[0x0C] != 0)
        delayCnt = (uint32_t)nvs_datas.modbus.reg_3000[0x0C] * 60 * TICK_PER_SECOND;
    
    if(ST_RUN == vfd.ctrl.llc_st)    delayCnt++;      
    else   delayCnt = 0;
    
    //_IsFault  =  ((nvs_datas.modbus.reg_3000[0x0C] != 0) 
    //              && (delayCnt >= (nvs_datas.modbus.reg_3000[0x0C] * 60 * TICK_PER_SECOND));
    _IsFault  =  0;
               
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}


/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OVP_P_BUS(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  FHD_ERROR && (!DIO_READ_BIT(OVP_P_BUS_PIN)  || (DIO_IRQ_DELAY(OVP_P_BUS_PIN) > 0));
    _IsFault = _IsFault && VFD_VIN_OK;
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
    if(!VFD_VIN_OK && DIO_READ_BIT(OVP_P_BUS_PIN) && (DIO_IRQ_DELAY(OVP_P_BUS_PIN) != 0))   DIO_IRQ_DELAY(OVP_P_BUS_PIN) = 0;
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ICP(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = FHD_ERROR && (!DIO_READ_BIT(ICP_PIN) || (DIO_IRQ_DELAY(ICP_PIN) > 0));
    _IsFault = _IsFault && VFD_VIN_OK && VFD_ENABLE_AC380;           
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
    if(!VFD_VIN_OK && DIO_READ_BIT(ICP_PIN) && (DIO_IRQ_DELAY(ICP_PIN) != 0))   DIO_IRQ_DELAY(ICP_PIN) = 0;
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_OCP(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  FHD_ERROR && (!DIO_READ_BIT(OCP_PIN) || (DIO_IRQ_DELAY(OCP_PIN) > 0)) ;
    _IsFault = _IsFault && VFD_VIN_OK && FHD_ERROR;           
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   if(!VFD_VIN_OK && DIO_READ_BIT(OCP_PIN) && (DIO_IRQ_DELAY(OCP_PIN) != 0))   DIO_IRQ_DELAY(OCP_PIN) = 0;
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_InverterIPM_P(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  FHD_ERROR && (!DIO_READ_BIT(OCP_PIN) || (DIO_IRQ_DELAY(OCP_PIN) > 0)) ;
    _IsFault = _IsFault && ((!DIO_READ_BIT(F_IPM_INV_PIN)   || (DIO_IRQ_DELAY(F_IPM_INV_PIN) > 0))    || 
                            LL_TIM_IsActiveFlag_BRK(TIM1)   ||  LL_TIM_IsActiveFlag_BRK2(TIM1));
    _IsFault = _IsFault && VFD_VIN_OK && FHD_ERROR;           
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
    if(!VFD_VIN_OK && DIO_READ_BIT(OCP_PIN) && (DIO_IRQ_DELAY(OCP_PIN) != 0))   DIO_IRQ_DELAY(OCP_PIN) = 0;
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_InverterIPM_N(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    //_IsFault =  FHD_ERROR && (!DIO_READ_BIT(OCP_N_PIN)  || (DIO_IRQ_DELAY(OCP_N_PIN) > 0)) ;
    _IsFault = _IsFault && ((!DIO_READ_BIT(F_IPM_INV_PIN)   || (DIO_IRQ_DELAY(F_IPM_INV_PIN) > 0))    || 
                            LL_TIM_IsActiveFlag_BRK(TIM1)   ||  LL_TIM_IsActiveFlag_BRK2(TIM1));
    _IsFault = _IsFault && VFD_VIN_OK && FHD_ERROR;           
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
    //if(!VFD_VIN_OK && DIO_READ_BIT(OCP_N_PIN) && (DIO_IRQ_DELAY(OCP_N_PIN) != 0))   DIO_IRQ_DELAY(OCP_N_PIN) = 0;
}
/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_F_HD(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (!DIO_READ_BIT(F_HD_PIN)  || (DIO_IRQ_DELAY(F_HD_PIN) > 0)) ;
    _IsFault = _IsFault && !vfd.diag.dc_44 && !vfd.diag.dc_03 && !vfd.diag.dc_04 && !vfd.diag.dc_05 && !vfd.diag.dc_79 && !vfd.diag.dc_80 && !vfd.diag.dc_81 && !vfd.diag.dc_82;
    _IsFault = _IsFault && VFD_VIN_OK ;             
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
    
    if(!VFD_VIN_OK && DIO_READ_BIT(F_HD_PIN) && (DIO_IRQ_DELAY(F_HD_PIN) != 0))   DIO_IRQ_DELAY(F_HD_PIN) = 0;
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_DCDC_StartupFailed(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
     ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  0;
               
    _IsNormal = !_IsFault;
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}


/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_POW(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    static uint32_t activeCnt = 0;
     ///////////////////////////////////////////////////////////////////////////////////
    
    if(DIO_READ_BIT(POW_PIN) == 0)
    {
        activeCnt++;
        if((activeCnt > 3)&&(activeCnt <= 6000)) hardware_irq_pin.POW_flag = 1;
    }
    else
    {
        activeCnt = 0;
    }
    
    _IsFault =  hardware_irq_pin.POW_flag && (activeCnt <= 6000);
               
    _IsNormal = DIO_READ_BIT(POW_PIN) || (activeCnt > 6000);
               
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
    
    if(diag_bit)
        hardware_irq_pin.POW_flag = 0;
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_Precharge_KMON3(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;


    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  VFD_ENABLE_AC380 && (vfd.precharge_kmon3_try >= 2);

    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
    if(diag_bit && !vfd.bit.lock_stop)
        vfd.precharge_kmon3_try = 0;
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_bus_short_circuit(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    uint8_t fast_over = ((fabs(DPLL3P.Iac.In.a) >= 2.828f) || (fabs(DPLL3P.Iac.In.b) >= 2.828f) || (fabs(DPLL3P.Iac.In.c) >= 2.828f));
    uint8_t rms_over = ((vfd.fast_ad.ac_iin_r >= 2.0f) || (vfd.fast_ad.ac_iin_r >= 2.0f) || (vfd.fast_ad.ac_iin_r >= 2.0f));
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (vfd.io.kmon3 || vfd.io.kmon1 ) && (vfd.fast_ad.vbus_inv <= 20)\
                && 
                (fast_over || rms_over);
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
   
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_VBUS_SensorError(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  (diagon_tick < 500) && ((vfd.fast_ad.vbus_inv >= 800) || (vfd.fast_ad.vbus_inv >= 800));
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_VIN_R_SensorError(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = Fast_DC15V_5V_Is_Ok &&  Fast_DC15V_5V_Is_Ok && VFD_SWITCH_AC380 && (diagon_tick < 500) && (acvin_r._rms >= 500);
    _IsNormal = !VFD_SWITCH_AC380 || (acvin_r._rms < 500);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_VIN_S_SensorError(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  Fast_DC15V_5V_Is_Ok && Fast_DC15V_5V_Is_Ok && VFD_SWITCH_AC380 && (diagon_tick < 500) && (acvin_s._rms >= 500);
    _IsNormal = !VFD_SWITCH_AC380 || (acvin_s._rms < 500);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_VIN_T_SensorError(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = Fast_DC15V_5V_Is_Ok && Fast_DC15V_5V_Is_Ok && VFD_SWITCH_AC380 && (diagon_tick < 500) && (acvin_t._rms >= 500);
    _IsNormal = !VFD_SWITCH_AC380 || (acvin_t._rms < 500);
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_IIN_R_SensorError(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = Fast_DC15V_5V_Is_Ok && VFD_SWITCH_AC380 && (diagon_tick < 500) && (aciin_r._rms >= 5.0f);
    _IsNormal = !VFD_SWITCH_AC380 || (aciin_r._rms < 5.0f);
    

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_IIN_S_SensorError(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault = Fast_DC15V_5V_Is_Ok && VFD_SWITCH_AC380 && (diagon_tick < 500) && (aciin_s._rms >= 5.0f);
    _IsNormal = !VFD_SWITCH_AC380 || (aciin_s._rms < 5.0f);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_IIN_T_SensorError(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = Fast_DC15V_5V_Is_Ok && VFD_SWITCH_AC380 && (diagon_tick < 500) && (aciin_t._rms >= 5.0f);
    _IsNormal = !VFD_SWITCH_AC380 || (aciin_t._rms < 5.0f);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_IOUT_U_SensorError(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  Fast_DC15V_5V_Is_Ok && (diagon_tick < 500) && (vfd.fast_ad.ac_iout_u >= 5.0f);
    _IsNormal = (vfd.fast_ad.ac_iout_u < 5.0f);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_IOUT_V_SensorError(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  Fast_DC15V_5V_Is_Ok && (diagon_tick < 500) && (vfd.fast_ad.ac_iout_v >= 5.0f);
    _IsNormal = (vfd.fast_ad.ac_iout_v < 5.0f);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_IOUT_W_SensorError(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    
    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault =  Fast_DC15V_5V_Is_Ok && (diagon_tick < 500)  && (vfd.fast_ad.ac_iout_w >= 5.0f);
    _IsNormal = (vfd.fast_ad.ac_iout_w < 5.0f);

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_Overload110(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    float power = 0;
    
    power = vfd.acout_capacity;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  (power >= (IOUT_OVERLOAD_BASE*1.1f));

    _IsNormal = (power <= IOUT_OVERLOAD_BASE);
  

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_Overload120(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    float power = 0;
    
    power = vfd.acout_capacity;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  (power >= (IOUT_OVERLOAD_BASE*1.2f));

    _IsNormal = (power <= IOUT_OVERLOAD_BASE);
  

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_Overload130(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    float power = 0;
    
    power = vfd.acout_capacity;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  (power >= (IOUT_OVERLOAD_BASE*1.3f));

    _IsNormal = (power <= IOUT_OVERLOAD_BASE);
  

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_Overload140(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    float power = 0;
    
    power = vfd.acout_capacity;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  (power >= (IOUT_OVERLOAD_BASE*1.4f));

    _IsNormal = (power <= IOUT_OVERLOAD_BASE);
  

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_Overload150(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    float power = 0;
    
    power = vfd.acout_capacity;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  (power >= (IOUT_OVERLOAD_BASE*1.5f));

    _IsNormal = (power <= IOUT_OVERLOAD_BASE);
  

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_Overload160(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    float power = 0;
    
    power = vfd.acout_capacity;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  (power >= (IOUT_OVERLOAD_BASE*1.6f));

    _IsNormal = (power <= IOUT_OVERLOAD_BASE);
  

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_Overload170(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    float power = 0;
    
    power = vfd.acout_capacity;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  (power >= (IOUT_OVERLOAD_BASE*1.7f));

    _IsNormal = (power <= IOUT_OVERLOAD_BASE);
  

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_Overload180(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    float power = 0;
    
    power = vfd.acout_capacity;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  (power >= (IOUT_OVERLOAD_BASE*1.8f));

    _IsNormal = (power <= IOUT_OVERLOAD_BASE);
  

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_ACIout_Overload190(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    float power = 0;
    
    power = vfd.acout_capacity;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  =  (power >= (IOUT_OVERLOAD_BASE*1.9f));

    _IsNormal = (power <= IOUT_OVERLOAD_BASE);
  

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief AC输入过载保护110%
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIin_Overload110(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief AC输入过载保护120%
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIin_Overload120(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief AC输入过载保护130%
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIin_Overload130(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief AC输入过载保护140%
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIin_Overload140(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief AC输入过载保护150%
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIin_Overload150(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief AC输入过载保护160%
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIin_Overload160(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief AC输入过载保护170%
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIin_Overload170(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief AC输入过载保护180%
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIin_Overload180(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief AC输入过载保护190%
  * @param diag 故障诊断位数组指针
  * @param offset 故障码在数组中的偏移量
  * @retval 无
  */
void DiagLgc_ACIin_Overload190(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);

    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief
  * @param
  * @retval
  */
void DiagLgc_TemptSensor_Error_1(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_TemptSensor_Error_2(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;

    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_TemptSensor_Error_3(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;

    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_TemptSensor_Error_4(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;

    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_TemptSensor_Error_5(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;

    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_TemptSensor_Error_6(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;

    ///////////////////////////////////////////////////////////////////////////////////
    _IsFault  = 0;
    _IsNormal = !_IsFault;
  

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_TemptSensor_Error_7(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;

    _IsFault  = 0;
    _IsNormal = !_IsFault;
    
    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}

/**
  * @brief 
  * @param 
  * @retval 
  */
void DiagLgc_TemptSensor_Error_8(uint8_t *diag,int offset)
{
    static uint32_t faultCnt = 0;
    static uint32_t normalCnt = 0;
    uint8_t diag_bit = toolbox_read_bit(diag,offset);
    uint8_t _IsFault=0, _IsNormal;
    
    if(!vfd.bit.ad_init)
        return;
    
    _IsFault  = 0;
    _IsNormal = !_IsFault;

    diag_bit = duplex_relay_ticking(diag_bit,               // lock fault until normalCnt timeout
                                      _IsFault, &faultCnt, diag_table[offset].happen_cnt,
                                      _IsNormal, &normalCnt, diag_table[offset].resume_cnt);
    
    toolbox_write_bit(diag,offset,diag_bit);
}





/**
  * @brief 
  * @param 
  * @retval 
  */
void diag_logic_all(void)
{
//    #ifdef VFD_TEST_DEBUG
//    return;  //test mode donot diag
//    #endif

    DiagLgc_AD_Ref(&vfd.diag,           23);
    DiagLgc_12V_Over(&vfd.diag,         24);    
    DiagLgc_12V_Low(&vfd.diag,          25);    
    DiagLgc_5V_Over(&vfd.diag,          26);    
    DiagLgc_5V_Low(&vfd.diag,           27);
    
    if(!vfd.diag.dc_23 &&
       !vfd.diag.dc_24 &&
       !vfd.diag.dc_25 &&
       !vfd.diag.dc_26 &&
       !vfd.diag.dc_27 &&
        (vfd.bit.io_init || (rt_tick_get() > 5*RT_TICK_PER_SECOND))
    )
    {
        diagon_tick++;
        DiagLgc_ACIinLimit(&vfd.diag, 0);            // DiagLgc_ACIinLimit
        DiagLgc_ACIoutLimit(&vfd.diag, 1);           // DiagLgc_ACIoutLimit  
        DiagLgc_InverterIPM(&vfd.diag, 2);           // DiagLgc_InverterIPM
        DiagLgc_PfcIPM(&vfd.diag, 3);                // DiagLgc_PfcIPM
        DiagLgc_ACVin_Over(&vfd.diag, 4);            // DiagLgc_ACVin_Over
        DiagLgc_ACVin_Low(&vfd.diag, 5);             // DiagLgc_ACVin_Low
        DiagLgc_Vbus_Over1(&vfd.diag, 6);            // DiagLgc_Vbus_Over1
        DiagLgc_Vbus_Over2(&vfd.diag, 7);            // DiagLgc_Vbus_Over2
        DiagLgc_Vbus_Low(&vfd.diag, 8);              // DiagLgc_Vbus_Low
        DiagLgc_DCVin_Over(&vfd.diag, 9);            // DiagLgc_DCVin_Over
        DiagLgc_DCVin_Low(&vfd.diag, 10);            // DiagLgc_DCVin_Low
        DiagLgc_ACVin_LosePhaseR(&vfd.diag, 11);     // DiagLgc_ACVin_LosePhaseR
        DiagLgc_ACVin_LosePhaseS(&vfd.diag, 12);     // DiagLgc_ACVin_LosePhaseS
        DiagLgc_ACVin_LosePhaseT(&vfd.diag, 13);     // DiagLgc_ACVin_LosePhaseT
        DiagLgc_ACIout_LosePhaseU(&vfd.diag, 14);    // DiagLgc_ACIout_LosePhaseU
        DiagLgc_ACIout_LosePhaseV(&vfd.diag, 15);    // DiagLgc_ACIout_LosePhaseV
        DiagLgc_ACIout_LosePhaseW(&vfd.diag, 16);    // DiagLgc_ACIout_LosePhaseW
        DiagLgc_AC_KMON1(&vfd.diag, 17);             // DiagLgc_AC_KMON1
        DiagLgc_PrechargeResist(&vfd.diag, 18);      // DiagLgc_PrechargeResist
        DiagLgc_ACVin_PhaseSeqc(&vfd.diag, 19);      // DiagLgc_ACVin_PhaseSeqc
        DiagLgc_ACVin_FreqLow(&vfd.diag, 20);        // DiagLgc_ACVin_FreqLow
        DiagLgc_ACVin_FreqOver(&vfd.diag, 21);       // DiagLgc_ACVin_FreqOver
        DiagLgc_SpiFlashMountErr(&vfd.diag, 22);     // DiagLgc_SpiFlashMountErr
        DiagLgc_AD_Ref(&vfd.diag, 23);               // DiagLgc_AD_Ref
        // DiagLgc_12V_Over(&vfd.diag, 24);             // DiagLgc_12V_Over
        // DiagLgc_12V_Low(&vfd.diag, 25);              // DiagLgc_12V_Low
        // DiagLgc_5V_Over(&vfd.diag, 26);              // DiagLgc_5V_Over
        // DiagLgc_5V_Low(&vfd.diag, 27);               // DiagLgc_5V_Low
        DiagLgc_MainPowerMisWire(&vfd.diag, 28);     // DiagLgc_MainPowerMisWire
        DiagLgc_ACIout_GroudU(&vfd.diag, 29);        // DiagLgc_ACIout_GroudU
        DiagLgc_ACIout_GroudV(&vfd.diag, 30);        // DiagLgc_ACIout_GroudV
        DiagLgc_ACIout_GroudW(&vfd.diag, 31);        // DiagLgc_ACIout_GroudW
        DiagLgc_Com485Timeout(&vfd.diag, 32);        // DiagLgc_Com485Timeout
        DiagLgc_ComCanTimeout(&vfd.diag, 33);        // DiagLgc_ComCanTimeout
        DiagLgc_TempLimit(&vfd.diag, 34);            // DiagLgc_TempLimit
        DiagLgc_Lock(&vfd.diag, 35);                 // DiagLgc_Lock
        DiagLgc_OVP_P_BUS(&vfd.diag, 36);           // DiagLgc_OVP_P_BUS
        DiagLgc_ICP(&vfd.diag, 37);                  // DiagLgc_ICP
        DiagLgc_OCP(&vfd.diag, 38);                  // DiagLgc_OCP
        DiagLgc_F_HD(&vfd.diag, 39);                 // DiagLgc_F_HD
        DiagLgc_POW(&vfd.diag, 40);                  // DiagLgc_POW
        DiagLgc_ACIout_Overload110(&vfd.diag, 41);   // DiagLgc_ACIout_Overload110
        DiagLgc_ACIout_Overload120(&vfd.diag, 42);   // DiagLgc_ACIout_Overload120
        DiagLgc_ACIout_Overload130(&vfd.diag, 43);   // DiagLgc_ACIout_Overload130
        DiagLgc_ACIout_Overload140(&vfd.diag, 44);   // DiagLgc_ACIout_Overload140
        DiagLgc_ACIout_Overload150(&vfd.diag, 45);   // DiagLgc_ACIout_Overload150
        DiagLgc_ACIout_Overload160(&vfd.diag, 46);   // DiagLgc_ACIout_Overload160
        DiagLgc_ACIout_Overload170(&vfd.diag, 47);   // DiagLgc_ACIout_Overload170
        DiagLgc_ACIout_Overload180(&vfd.diag, 48);   // DiagLgc_ACIout_Overload180
        DiagLgc_ACIout_Overload190(&vfd.diag, 49);   // DiagLgc_ACIout_Overload190
        DiagLgc_ACIin_Overload110(&vfd.diag, 50);    // DiagLgc_ACIin_Overload110
        DiagLgc_ACIin_Overload120(&vfd.diag, 51);    // DiagLgc_ACIin_Overload120
        DiagLgc_ACIin_Overload130(&vfd.diag, 52);    // DiagLgc_ACIin_Overload130
        DiagLgc_ACIin_Overload140(&vfd.diag, 53);    // DiagLgc_ACIin_Overload140
        DiagLgc_ACIin_Overload150(&vfd.diag, 54);    // DiagLgc_ACIin_Overload150
        DiagLgc_ACIin_Overload160(&vfd.diag, 55);    // DiagLgc_ACIin_Overload160
        DiagLgc_ACIin_Overload170(&vfd.diag, 56);    // DiagLgc_ACIin_Overload170
        DiagLgc_ACIin_Overload180(&vfd.diag, 57);    // DiagLgc_ACIin_Overload180
        DiagLgc_ACIin_Overload190(&vfd.diag, 58);    // DiagLgc_ACIin_Overload190
        DiagLgc_OverTempt_Warn_1(&vfd.diag, 59);     // DiagLgc_OverTempt_Warn_1
        DiagLgc_OverTempt_Warn_2(&vfd.diag, 60);     // DiagLgc_OverTempt_Warn_2
        DiagLgc_OverTempt_Warn_3(&vfd.diag, 61);     // DiagLgc_OverTempt_Warn_3
        DiagLgc_OverTempt_Warn_4(&vfd.diag, 62);     // DiagLgc_OverTempt_Warn_4
        DiagLgc_OverTempt_Warn_5(&vfd.diag, 63);     // DiagLgc_OverTempt_Warn_5
        DiagLgc_OverTempt_Warn_6(&vfd.diag, 64);     // DiagLgc_OverTempt_Warn_6
        DiagLgc_OverTempt_Warn_7(&vfd.diag, 65);     // DiagLgc_OverTempt_Warn_7
        DiagLgc_OverTempt_Warn_8(&vfd.diag, 66);     // DiagLgc_OverTempt_Warn_8
        DiagLgc_OverMosi(&vfd.diag, 67);             // DiagLgc_OverMosi
        DiagLgc_OverTempt_Stop_1(&vfd.diag, 68);     // DiagLgc_OverTempt_Stop_1
        DiagLgc_OverTempt_Stop_2(&vfd.diag, 69);     // DiagLgc_OverTempt_Stop_2
        DiagLgc_OverTempt_Stop_3(&vfd.diag, 70);     // DiagLgc_OverTempt_Stop_3
        DiagLgc_OverTempt_Stop_4(&vfd.diag, 71);     // DiagLgc_OverTempt_Stop_4
        DiagLgc_OverTempt_Stop_5(&vfd.diag, 72);     // DiagLgc_OverTempt_Stop_5
        DiagLgc_OverTempt_Stop_6(&vfd.diag, 73);     // DiagLgc_OverTempt_Stop_6
        DiagLgc_OverTempt_Stop_7(&vfd.diag, 74);     // DiagLgc_OverTempt_Stop_7
        DiagLgc_OverTempt_Stop_8(&vfd.diag, 75);     // DiagLgc_OverTempt_Stop_8
        DiagLgc_bus_short_circuit(&vfd.diag, 76);    // DiagLgc_bus_short_circuit
        DiagLgc_DCVin_Low_Warning(&vfd.diag, 77);     // DiagLgc_DCVin_Low_Warning
        DiagLgc_ACIN_Invalid(&vfd.diag, 78);          // DiagLgc_ACIN_Invalid
        DiagLgc_DCIN_Invalid(&vfd.diag, 79);          // DiagLgc_DCIN_Invalid
        DiagLgc_VBUS_SensorError(&vfd.diag, 80);     // DiagLgc_VBUS_SensorError
        DiagLgc_VIN_R_SensorError(&vfd.diag, 81);    // DiagLgc_VIN_R_SensorError
        DiagLgc_VIN_S_SensorError(&vfd.diag, 82);    // DiagLgc_VIN_S_SensorError
        DiagLgc_VIN_T_SensorError(&vfd.diag, 83);    // DiagLgc_VIN_T_SensorError
        DiagLgc_IIN_R_SensorError(&vfd.diag, 84);    // DiagLgc_IIN_R_SensorError
        DiagLgc_IIN_S_SensorError(&vfd.diag, 85);    // DiagLgc_IIN_S_SensorError
        DiagLgc_IIN_T_SensorError(&vfd.diag, 86);    // DiagLgc_IIN_T_SensorError
        DiagLgc_IOUT_U_SensorError(&vfd.diag, 87);   // DiagLgc_IOUT_U_SensorError
        DiagLgc_IOUT_V_SensorError(&vfd.diag, 88);   // DiagLgc_IOUT_V_SensorError
        DiagLgc_IOUT_W_SensorError(&vfd.diag, 89);   // DiagLgc_IOUT_W_SensorError
        DiagLgc_TemptSensor_Error_1(&vfd.diag, 90);  // DiagLgc_TemptSensor_Error_1
        DiagLgc_TemptSensor_Error_2(&vfd.diag, 91);  // DiagLgc_TemptSensor_Error_2
        DiagLgc_TemptSensor_Error_3(&vfd.diag, 92);  // DiagLgc_TemptSensor_Error_3
        DiagLgc_TemptSensor_Error_4(&vfd.diag, 93);  // DiagLgc_TemptSensor_Error_4
        DiagLgc_TemptSensor_Error_5(&vfd.diag, 94);  // DiagLgc_TemptSensor_Error_5
        DiagLgc_TemptSensor_Error_6(&vfd.diag, 95);  // DiagLgc_TemptSensor_Error_6
        DiagLgc_TemptSensor_Error_7(&vfd.diag, 96);  // DiagLgc_TemptSensor_Error_7
        DiagLgc_TemptSensor_Error_8(&vfd.diag, 97);  // DiagLgc_TemptSensor_Error_8
        
    }                                           
    
}